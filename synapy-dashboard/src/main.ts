import { createApp } from 'vue';
import { createPinia } from 'pinia'
import App from './App.vue';
import router from './router';
import vuetify from './plugins/vuetify';
import { useAuthStore } from '@/store'
import '@/scss/style.scss';
import VueApexCharts from 'vue3-apexcharts';
import VueTablerIcons from 'vue-tabler-icons';
import Maska from 'maska';
import '@mdi/font/css/materialdesignicons.css'

const app = createApp(App);

app.use(createPinia())
app.use(router);
useAuthStore().initAuthListener();
app.use(VueTablerIcons);
app.use(Maska);
app.use(VueApexCharts);
app.use(vuetify)

app.mount('#app');
