<script setup>
defineProps({
  title: String,
  subtitle: String,
  help: String,
  divider: <PERSON><PERSON><PERSON>
})
</script>

<template>
  <v-row
    class="py-6 align-center settings-row justify-start"
    :class="{ 'with-divider': divider }"
    no-gutters
  >
    <v-col cols="12" md="4" class="pr-md-4 mb-2 mb-md-0">
      <div class="d-flex align-center mb-1">
        <div class="text-body-1 font-weight-medium">{{ title }}</div>
        <v-tooltip v-if="help" text="Help">
          <template #activator="{ props }">
            <v-icon
              v-bind="props"
              icon="mdi-help-circle-outline"
              class="ml-2 text-medium-emphasis"
              size="18"
            />
          </template>
          <span>{{ help }}</span>
        </v-tooltip>
      </div>
      <div class="text-body-2 text-medium-emphasis">{{ subtitle }}</div>
    </v-col>

    <v-col cols="12" md="8" class="input-wrapper">
      <slot />
    </v-col>
  </v-row>
</template>

<style scoped>
.settings-row.with-divider {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.input-wrapper {
  max-width: 420px;
}
</style>
