<script setup lang="ts">
import { format } from "date-fns";
import { ref, computed } from "vue";
import { Icon } from '@iconify/vue';
import UiParentCard from '@/components/shared/UiParentCard.vue';
import { CallRecordData } from '@/data/Dashboard';

const rowsPerPage = ref(10);

const formatDate = (datetime: string | number | Date) => {
    return format(new Date(datetime), "MMMM d, yyyy");
};

const formatTime = (datetime: string | number | Date) => {
    return new Intl.DateTimeFormat("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
        timeZoneName: "short"
    }).format(new Date(datetime));
};

const formatDuration = (seconds: number) => {
    const min = Math.floor(seconds / 60);
    const sec = seconds % 60;
    return `${min} min ${sec} sec`;
};

const sortedCallRecords = computed(() => {
    return [...CallRecordData].sort((a, b) => new Date(b.datetime).getTime() - new Date(a.datetime).getTime());
});
</script>

<template>
  <v-container v-if="sortedCallRecords.length == 0" class="d-flex flex-row fill-height h-screen">
    <v-row class="justify-center mt-n16">
      <v-col cols="12" sm="8" md="6" lg="5">
        <div class="d-flex flex-column align-start">
          <div class="align-self-start mb-6">
            <div class="icon-circle d-flex align-center justify-center text-borderColor">
              <Icon icon="mdi:phone-log-outline" width="40" height="40" class="text-grey-darken-1" />
            </div>
          </div>

          <h1 class="text-h4 font-weight-bold mb-2">Historial de llamadas</h1>

          <p class="text-body-1 text-medium-emphasis mb-6">
            En esta página podrás ver con detalle todas las interacciones de tus asistentes.<br><br>
            Tu historial aún esta vacío, empieza a usar tu asistente para ver todo su potencial.
          </p>
        </div>
      </v-col>
    </v-row>
  </v-container>

  <div v-else fluid class="fill-height">
    <div class="px-6 py-4">
      <div class="d-flex justify-space-between align-center mb-4">
        <h1 class="text-h3 font-weight-medium">Historial de Llamadas</h1>
      </div>

      <v-table>
        <thead>
          <tr>
            <th class="text-uppercase text-body-2 font-weight-medium text-medium-emphasis">Fecha y hora</th>
            <th class="text-uppercase text-body-2 font-weight-medium text-medium-emphasis">Duración</th>
            <th class="text-uppercase text-body-2 font-weight-medium text-medium-emphasis">Origen/Destino</th>
            <th class="text-uppercase text-body-2 font-weight-medium text-medium-emphasis">ID de Llamada</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in sortedCallRecords" :key="item.callId" class="month-item">
            <td>
              <div class="d-flex align-center">
                <div class="mx-3">
                  <h6 class="text-subtitle-1 text-no-wrap font-weight-medium">
                    {{ formatDate(item.datetime) }}
                  </h6>
                  <span class="text-body-1 text-no-wrap text-textSecondary">
                    {{ formatTime(item.datetime) }}
                  </span>
                </div>
              </div>
            </td>
            <td>
              <p class="text-no-wrap text-body-1 text-textSecondary">
                {{ formatDuration(item.durationSeconds) }}
              </p>
            </td>
            <td>
              <v-chip rounded="sm" class="font-weight-semibold" color="success" size="small" label>
                {{ item.from }}
              </v-chip>
            </td>
            <td>
              <p class="text-body-1">{{ item.callId }}</p>
            </td>
          </tr>
        </tbody>
      </v-table>

      <!-- Paginación -->
      <div class="d-flex align-center justify-space-between mt-4">
        <div class="d-flex align-center">
          <v-btn icon="mdi-chevron-double-left" variant="text" size="small" class="mr-1"></v-btn>
          <v-btn icon="mdi-chevron-left" variant="text" size="small" class="mr-1"></v-btn>
          <span class="text-body-2 mx-2">Page 1 of 1</span>
          <v-btn icon="mdi-chevron-right" variant="text" size="small" class="ml-1"></v-btn>
          <v-btn icon="mdi-chevron-double-right" variant="text" size="small" class="ml-1"></v-btn>
        </div>
        <div class="d-flex align-center">
          <span class="text-body-2 mr-2">Rows per page:</span>
          <v-select
            v-model="rowsPerPage"
            :items="[10, 20, 50, 100]"
            variant="plain"
            density="compact"
            hide-details
            class="rows-select"
            style="width: 70px;"
          ></v-select>
          <span class="text-body-2 ml-4">1 result</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border-style: solid;
}
</style>
