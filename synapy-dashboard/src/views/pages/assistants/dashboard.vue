<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { Icon } from '@iconify/vue';
import AssistantTypeSelector from '@/components/AssistantTypeSelector.vue';
import SettingRow from '@/components/SettingRow.vue';
import NavItem from '@/layouts/full/vertical-sidebar/NavItem/index.vue';
import { useAssistantsStore, useProfileStore } from '@/store';
import type { Assistant } from '@/types/domain';

const DEBOUNCE_DELAY = 2000;

const showModal = ref(false);
const showCompanyNamePanel = ref(false);
const tab = ref(0);

const assistantsStore = useAssistantsStore();
const profileStore = useProfileStore();
const { companyName } = storeToRefs(profileStore);

const selectedAssistant = computed(() => assistantsStore.selectedAssistant);

// Reactive inputs
const assistantName = ref('');
const assistantType = ref('reservations');
const voiceId = ref('');
const timezone = ref('Europe/Madrid');
const patienceLevel = ref('low');
const stability = ref(0.5);
const voiceSpeed = ref(0.5);
const backchannelInput = ref('');
const backchannelWords = ref<string[]>([]);

const isInitializing = ref(false);
const originalValues = ref<Partial<Assistant>>({});
let updateTimeout: number | null = null;

const cards = [
  {
    icon: 'solar:calendar-broken',
    title: 'Gestor de citas',
    subtitle: 'Crea un asistente para gestionar citas y reservas',
    type: 'reservations',
  },
  {
    icon: 'solar:buildings-broken',
    title: 'Captación de activos',
    subtitle: 'Automatiza llamadas a particulares que quieran vender su casa',
    type: 'prospection',
  },
  {
    icon: 'solar:user-speak-broken',
    title: 'Asesor inmobiliario',
    subtitle: 'Asesora clientes potenciales en la búsqueda y filtrado de propiedades',
    type: 'advisor',
  },
]
const assistantTypes = [
  { text: 'Gestor de Citas', value: 'reservations' },
  { text: 'Captación de Activos', value: 'prospection' },
  { text: 'Asesor Inmobiliario', value: 'advisor' },
];

function clearUpdateTimeout() {
  if (updateTimeout) {
    clearTimeout(updateTimeout);
    updateTimeout = null;
  }
}

function captureOriginalValues(assistant: Assistant) {
  originalValues.value = {
    name: assistant.name || '',
    type: assistant.type || 'reservations',
    voiceId: assistant.voiceId || '',
    timezone: assistant.timezone || 'Europe/Madrid',
    patienceLevel: assistant.patienceLevel || 'low',
    stability: assistant.stability ?? 0.5,
    voiceSpeed: assistant.voiceSpeed ?? 0.5,
    backchannelWords: assistant.backchannelWords ? [...assistant.backchannelWords] : [],
  };
}

function getChangedFields() {
  const changes: Partial<Assistant> = {};
  const current = {
    name: assistantName.value,
    type: assistantType.value,
    voiceId: voiceId.value,
    timezone: timezone.value,
    patienceLevel: patienceLevel.value,
    stability: stability.value,
    voiceSpeed: voiceSpeed.value,
    backchannelWords: backchannelWords.value,
  };

  // Compare each field with the original values
  if (current.name !== originalValues.value.name) {
    changes.name = current.name;
  }
  if (current.type !== originalValues.value.type) {
    changes.type = current.type;
  }
  if (current.voiceId !== originalValues.value.voiceId) {
    changes.voiceId = current.voiceId;
  }
  if (current.timezone !== originalValues.value.timezone) {
    changes.timezone = current.timezone;
  }
  if (current.patienceLevel !== originalValues.value.patienceLevel) {
    changes.patienceLevel = current.patienceLevel;
  }
  if (current.stability !== originalValues.value.stability) {
    changes.stability = current.stability;
  }
  if (current.voiceSpeed !== originalValues.value.voiceSpeed) {
    changes.voiceSpeed = current.voiceSpeed;
  }
  const originalWords = originalValues.value.backchannelWords || [];
  const currentWords = current.backchannelWords || [];
  if (JSON.stringify(originalWords.sort()) !== JSON.stringify(currentWords.sort())) {
    changes.backchannelWords = currentWords;
  }

  return changes;
}

async function loadAssistant(assistantId: string) {
  console.debug('Loading assistant with ID:', assistantId);
  await assistantsStore.setSelectedAssistantId(assistantId);
  await assistantsStore.fetchAssistantDetailsById(assistantId);
}

function addBackchannelWord() {
  const value = backchannelInput.value.trim();
  if (value && !backchannelWords.value.includes(value)) {
    backchannelWords.value.push(value);
  }
  backchannelInput.value = '';
}

function removeBackchannelWord(index: number) {
  backchannelWords.value.splice(index, 1);
}

function updateAssistant() {
  clearUpdateTimeout();

  if (selectedAssistant.value && !isInitializing.value) {
    updateTimeout = setTimeout(() => {
      const changedFields = getChangedFields();

      // Update only in case there are changes
      if (Object.keys(changedFields).length > 0) {
        const assistantId = selectedAssistant.value!.id
        console.debug('Updating assistant with ID {', assistantId, '} with data:', changedFields);
        assistantsStore.updateAssistantById(assistantId, {
          id: assistantId,
          ...changedFields
        });

        // Update original values after a successful update
        captureOriginalValues({
          ...selectedAssistant.value,
          ...changedFields
        } as Assistant);
      }
      updateTimeout = null;
    }, DEBOUNCE_DELAY);
  }
}

function createAssistant(type: string) {
  if (!profileStore.companyName || profileStore.companyName === '') {
    assistantType.value = type;
    showCompanyNamePanel.value = true;
  } else {
    assistantsStore.createNewAssistant(type, profileStore.companyName);
  }
}

function submitCompanyName() {
  showCompanyNamePanel.value = false;
  profileStore.updateCompanyName(companyName.value!);
  createAssistant(assistantType.value);
}

function deleteAssistant() {
  if (selectedAssistant.value) {
    const assistantId = selectedAssistant.value.id;
    console.debug('Deleting assistant with ID:', assistantId);
    assistantsStore.deleteAssistantById(assistantId);
    assistantsStore.setSelectedAssistantId(null);
  }
}

onMounted(() => {
  assistantsStore.fetchAllAssistants();
  profileStore.fetchCompanyName();
});

// On assistant selection change, update the reactive inputs
watch(selectedAssistant, (assistant: Assistant | null) => {
  if (assistant) {
    clearUpdateTimeout();

    isInitializing.value = true;

    assistantName.value = assistant.name || '';
    assistantType.value = assistant.type || 'reservations';
    voiceId.value = assistant.voiceId || '';
    timezone.value = assistant.timezone || 'Europe/Madrid';
    patienceLevel.value = assistant.patienceLevel || 'low';
    stability.value = assistant.stability ?? 0.5;
    voiceSpeed.value = assistant.voiceSpeed ?? 0.5;
    backchannelWords.value = assistant.backchannelWords ? [...assistant.backchannelWords] : [];

    captureOriginalValues(assistant);

    nextTick(() => {
      isInitializing.value = false;
    });
  }
});

// Watch for changes in the reactive inputs and update the assistant
watch(
  [assistantName, assistantType, voiceId, timezone, patienceLevel, stability, voiceSpeed, backchannelWords],
  () => {
    updateAssistant();
  },
  { deep: true }
);

onUnmounted(() => {
  clearUpdateTimeout();
});
</script>

<template>
  <v-container fluid class="pa-0 d-flex flex-row fill-height h-screen justify-center">
    <v-progress-circular v-if="assistantsStore.isLoading"
      indeterminate
      color="primary"
      size="100"
    />

    <div v-else>
      <div v-if="assistantsStore.assistants.length === 0" class="mt-n16">
        <AssistantTypeSelector :cards="cards" @select="createAssistant" />
      </div>

      <div v-else>
        <v-row no-gutters class="h-screen">
          <v-col cols="2" class="bg-surface">
            <div class="scrollnavbar">
              <v-list nav dense>
                <v-list-subheader>ASISTENTES</v-list-subheader>
                <template v-for="assistant in assistantsStore.assistants" :key="assistant.id">
                  <NavItem
                    :active="selectedAssistant?.id === assistant.id"
                    :title="assistant.name"
                    class="leftPadding"
                    @click="loadAssistant(assistant.id)"
                  />
                </template>
              </v-list>
            </div>
          </v-col>

          <v-col cols="10">
            <v-alert type="warning" class="ma-4">
              Importante: Tu asistente no tiene número de teléfono y no puede hacer llamadas.
            </v-alert>

            <v-container fluid>
              <h2 class="text-h5 font-weight-bold">Dashboard</h2>

              <v-row class="align-center justify-space-between" no-gutters>
                <v-tabs v-model="tab" color="primary" align-tabs="title">
                  <v-tab><v-icon start icon="mdi-cog-outline" class="mr-1" /> General</v-tab>
                  <v-tab><v-icon start icon="mdi-volume-high" class="mr-1" /> Voz</v-tab>
                </v-tabs>

                <v-btn color="primary" variant="elevated" @click="showModal = true">
                  <Icon icon="mdi-plus" class="mr-2" />
                  <span>Añadir Asistente</span>
                </v-btn>
              </v-row>

              <v-card flat class="pa-6 rounded-xl h-100">
                <v-window v-model="tab" class="mt-3 pt-6">
                  <v-window-item>
                    <v-form>
                      <SettingRow
                        title="Nombre del Asistente"
                        subtitle="Elige un nombre para tu asistente. Este nombre será utilizado en todas las interacciones."
                        divider
                      >
                        <v-text-field label="Nombre del Asistente" v-model="assistantName" variant="outlined" />
                      </SettingRow>

                      <SettingRow
                        title="Tipo de Asistente"
                        subtitle="Elige el tipo de asistente que deseas crear. Esto afectará a su comportamiento y capacidades."
                        divider
                      >
                        <v-select
                          v-model="assistantType"
                          :items="assistantTypes"
                          item-title="text"
                          item-value="value"
                          label="Tipo de Asistente"
                          variant="outlined"
                        />
                      </SettingRow>

                      <SettingRow
                        title="Datos de Negocio"
                        subtitle="Asocia el asistente a una colección de datos. Esto le permitirá acceder a información específica de tu negocio."
                        divider
                      >
                        <v-btn color="primary" variant="outlined" class="mt-3">
                          <Icon icon="solar:add-square-broken" class="mr-2" />
                          <span>Añadir colección de datos</span>
                        </v-btn>
                      </SettingRow>

                      <SettingRow
                        title="Zona Horaria"
                        subtitle="Ajusta la zona horaria del asistente. Esto afecta a la hora y fecha de las respuestas."
                        divider
                      >
                        <v-select
                          v-model="timezone"
                          :items="['Europe/Madrid', 'Atlantic/Canary']"
                          label="Zona Horaria"
                          variant="outlined"
                        />
                      </SettingRow>

                      <SettingRow
                        title="Eliminar Asistente"
                        subtitle="Eliminar el asistente implica el borrado de todos los datos asociados a él. Esta acción no se puede deshacer."
                      >
                        <v-btn color="error" variant="outlined" class="mt-3" @click="deleteAssistant">
                          <Icon icon="solar:trash-bin-2-broken" class="mr-2" />
                          <span>Eliminar Asistente</span>
                        </v-btn>
                      </SettingRow>
                    </v-form>
                  </v-window-item>

                  <v-window-item>
                    <v-form>
                      <SettingRow
                        title="Voz"
                        subtitle="Elige la voz del asistente."
                        divider
                      >
                        <!-- TODO: Convert to dynamic form input to choose Voice ID -->
                        <v-card class="pa-4 mb-6" variant="outlined">
                          <div class="d-flex align-center justify-space-between">
                            <div>
                              <p class="font-weight-medium mb-1">Jessica</p>
                              <p class="text-caption">ElevenLabs • ID: cg...CkdW9 • American • Female</p>
                            </div>
                            <div class="d-flex gap-2">
                              <v-btn variant="text" icon="mdi-volume-high" size="small" />
                              <v-btn variant="outlined" size="small">Edit</v-btn>
                            </div>
                          </div>
                        </v-card>
                      </SettingRow>

                      <SettingRow
                        title="Nivel de Paciencia"
                        subtitle="Ajusta la velocidad de respuesta. Bajo para interacciones rápidas, alto para mayor atención en el diálogo y menos interrupciones."
                        divider
                      >
                        <v-btn-toggle v-model="patienceLevel" divided mandatory class="mb-6">
                          <v-btn value="low">Bajo<br><span class="text-caption">~1 seg.</span></v-btn>
                          <v-btn value="medium">Medio<br><span class="text-caption">~3 seg.</span></v-btn>
                          <v-btn value="high">Alto<br><span class="text-caption">~5 seg.</span></v-btn>
                        </v-btn-toggle>
                      </SettingRow>

                      <SettingRow
                        title="Estabilidad"
                        subtitle="Menor = más expresivo; Mayor = más estable"
                        :divider="true"
                      >
                        <v-slider v-model="stability" min="0" max="1" step="0.01" hide-details thumb-label="always" />
                      </SettingRow>

                      <SettingRow
                        title="Velocidad de la Voz"
                        subtitle="Ajusta la velocidad de la voz. Bajo para un tono más reflexivo, alto para un tono más rápido."
                        :divider="true"
                      >
                        <v-slider v-model="voiceSpeed" min="0" max="1" step="0.01" hide-details thumb-label="always" />
                      </SettingRow>

                      <SettingRow
                        title="Muletillas"
                        subtitle="Configura las muletillas a usar por tu asistente. Ejemplo: 'Ajá', 'Eh', 'Claro'."
                      >
                      <div class="d-flex flex-wrap align-center gap-2">
                        <v-chip
                          v-for="(chip, index) in backchannelWords"
                          :key="chip"
                          closable
                          @click:close="removeBackchannelWord(index)"
                          color="primary"
                          class="ma-1"
                        >
                          {{ chip }}
                        </v-chip>

                        <v-text-field
                          v-model="backchannelInput"
                          variant="outlined"
                          hide-details
                          placeholder="Añadir muletilla..."
                          class="ml-3"
                          single-line
                          @keyup.enter="addBackchannelWord"
                          style="min-width: 150px; max-width: 200px;"
                        >
                          <template #append-inner>
                            <v-icon @click="addBackchannelWord" class="cursor-pointer">mdi-plus</v-icon>
                          </template>
                        </v-text-field>
                      </div>
                      </SettingRow>
                    </v-form>
                  </v-window-item>
                </v-window>
              </v-card>
            </v-container>
          </v-col>
        </v-row>
      </div>
    </div>
  </v-container>

  <v-dialog v-model="showModal" max-width="1000">
    <v-card class="pa-6">
      <v-btn
        icon="mdi-close"
        class="ma-4"
        @click="showModal = false"
        style="position: absolute; top: 0; right: 0;"
      />

      <AssistantTypeSelector
        :cards="cards"
        @select="createAssistant"
      />
    </v-card>
  </v-dialog>

  <v-dialog v-model="showCompanyNamePanel" max-width="500">
    <v-card class="pa-6">
      <v-btn
        icon="mdi-close"
        class="ma-4"
        @click="showModal = false"
        style="position: absolute; top: 0; right: 0;"
      />

      <v-card-title class="text-h6 font-weight-bold pb-2">
        Nombre de la empresa
      </v-card-title>

      <v-card-text class="pt-0">
        <v-text-field
          v-model="companyName"
          label="Introduce el nombre de tu empresa"
          placeholder="Ej. Synapse Sea"
          variant="outlined"
          density="comfortable"
          autofocus
        />
      </v-card-text>

      <v-card-actions class="justify-end pt-2">
        <v-btn
          text="Cancelar"
          variant="text"
          color="grey"
          class="text-capitalize"
          @click="showCompanyNamePanel = false"
        />
        <v-btn
          text="Continuar"
          variant="flat"
          color="primary"
          class="text-capitalize"
          @click="submitCompanyName"
          :disabled="!companyName"
        />
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
