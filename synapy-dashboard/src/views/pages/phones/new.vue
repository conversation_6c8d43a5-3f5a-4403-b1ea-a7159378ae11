<script setup lang="ts">
import { PhoneNumbersData } from '@/data/Dashboard';
import type { PhoneNumber } from '@/types/domain';
import { Icon } from '@iconify/vue';
import { ref, computed } from "vue";

const phoneNumbers = ref(PhoneNumbersData);
const selectedPhone = ref(phoneNumbers.value[0]);
const rowsPerPage = ref(10);

const copyNumber = (number: string) => {
  navigator.clipboard.writeText(number);
};

const editPhone = (phone: PhoneNumber) => {
  // TODO: Call API to edit phone number
  console.log("Editing phone:", phone);
};

const deletePhone = (number: string) => {
  // TODO: Call API to delete phone number
  phoneNumbers.value = phoneNumbers.value.filter((phone) => phone.number !== number);
  if (selectedPhone.value.number === number) {
    selectedPhone.value = phoneNumbers.value[0] || {};
  }
};

const addPhone = () => {
  // TODO: Call API to add phone number
  const newPhone = {
    number: "+000000000",
    label: "New Number",
    type: "Main",
    active: false,
  };
  phoneNumbers.value.push(newPhone);
  selectedPhone.value = newPhone;
};

// const inboundAssistantName = computed({
//   get: () => selectedPhone.value.inboundAssistant?.name || "",
//   set: (newValue) => {
//     if (!selectedPhone.value.inboundAssistant) {
//       selectedPhone.value.inboundAssistant = { id: newValue.id, name: newValue.name };
//     } else {
//       selectedPhone.value.inboundAssistant.name = newValue;
//     }
//   }
// });
</script>

<template>
  <v-container v-if="phoneNumbers.length == 0" class="d-flex flex-row fill-height h-screen">
    <v-row class="justify-center mt-n16">
      <v-col cols="12" sm="8" md="6" lg="5">
        <div class="d-flex flex-column align-start">
          <div class="align-self-start mb-6">
            <div class="icon-circle d-flex align-center justify-center text-borderColor">
              <Icon icon="mdi:phone-log-outline" width="40" height="40" class="text-grey-darken-1" />
            </div>
          </div>

          <h1 class="text-h4 font-weight-bold mb-2">Números de Teléfono</h1>

          <p class="text-body-1 text-medium-emphasis mb-6">
            Esta es tu página de gestión de números de teléfono.<br>
            Aquí podrás ver todos tus números de teléfono.
          </p>

          <v-btn
            variant="elevated"
            color="primary"
            min-width="150"
            max-width="150"
            class="rounded-lg font-weight-medium text-none"
          >
            Agrega un número
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </v-container>

  <div v-else fluid class="fill-height">
    <div class="px-6 py-4">
      <div class="d-flex justify-space-between align-center mb-4">
        <h1 class="text-h3 font-weight-medium">Números de Teléfono</h1>
        <div class="d-flex">
          <v-btn
            color="primary"
            prepend-icon="mdi-plus"
          >
            Registar Nuevo Número
          </v-btn>
        </div>
      </div>

      <v-table>
        <thead>
          <tr>
            <th class="text-uppercase text-body-2 font-weight-medium text-medium-emphasis">Número</th>
            <th class="text-uppercase text-body-2 font-weight-medium text-medium-emphasis">Proveedor</th>
            <th class="text-uppercase text-body-2 font-weight-medium text-medium-emphasis">Estado</th>
            <th class="text-uppercase text-body-2 font-weight-medium text-medium-emphasis">Asistente</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              <div class="d-flex align-center">
                <span class="mr-2">🇺🇸</span>
                <span>+14123079942</span>
              </div>
            </td>
            <td>Twilio</td>
            <td>
              <v-chip
                color="success"
                size="small"
                variant="outlined"
                density="comfortable"
              >
                Active
              </v-chip>
            </td>
            <td><i>Sin Asignar</i></td>
            <td class="text-right">
              <v-btn icon="mdi-dots-vertical" variant="text" density="comfortable"></v-btn>
            </td>
          </tr>
        </tbody>
      </v-table>

      <!-- Paginación -->
      <div class="d-flex align-center justify-space-between mt-4">
        <div class="d-flex align-center">
          <v-btn icon="mdi-chevron-double-left" variant="text" size="small" class="mr-1"></v-btn>
          <v-btn icon="mdi-chevron-left" variant="text" size="small" class="mr-1"></v-btn>
          <span class="text-body-2 mx-2">Page 1 of 1</span>
          <v-btn icon="mdi-chevron-right" variant="text" size="small" class="ml-1"></v-btn>
          <v-btn icon="mdi-chevron-double-right" variant="text" size="small" class="ml-1"></v-btn>
        </div>
        <div class="d-flex align-center">
          <span class="text-body-2 mr-2">Rows per page:</span>
          <v-select
            v-model="rowsPerPage"
            :items="[10, 20, 50, 100]"
            variant="plain"
            density="compact"
            hide-details
            class="rows-select"
            style="width: 70px;"
          ></v-select>
          <span class="text-body-2 ml-4">1 result</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.opacity-50 {
  opacity: 0.5;
}

.icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border-style: solid;
}
</style>
