<script setup lang="ts">
import { useRouter } from 'vue-router';
import CardButton from '@/components/CardButton.vue';

const router = useRouter();
const cards = [
  {
    icon: 'solar:calendar-broken',
    title: 'Calendario',
    subtitle: 'Conecta con tu aplicación de calendario para gestionar citas y reservas',
    type: 'calendar',
  },
  {
    icon: 'solar:database-broken',
    title: 'Base de Datos',
    subtitle: 'Almacena los datos recolectados por tu asistente',
    type: 'database',
  },
]

function goToNewPage(type: string) {
  router.push({ name: 'integrations-new', query: { type: type } });
}
</script>

<template>
    <v-container class="d-flex flex-row fill-height h-screen">
    <div class="d-flex flex-column justify-center align-center text-center flex-grow-1 mt-n16">
      <div class="mb-10">
        <h1 class="text-h5 font-weight-bold mb-2">Configura tus integraciones</h1>
        <p class="text-body-1 text-medium-emphasis">
          Selecciona las integraciones que quieres activar para tu asistente
        </p>
      </div>

      <v-row class="w-100 justify-center" no-gutters>
        <v-col
          v-for="(card, index) in cards"
          :key="index"
          cols="12"
          sm="6"
          md="4"
          class="d-flex justify-center"
        >
          <CardButton
            :title="card.title"
            :subtitle="card.subtitle"
            :icon="card.icon"
            @click="goToNewPage(card.type)"
          />
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>
