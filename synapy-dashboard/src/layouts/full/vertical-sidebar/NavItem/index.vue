<script setup>
import { Icon } from '@iconify/vue';
const props = defineProps({
  type: String,
  to: String,
  disabled: Boolean,
  icon: String,
  BgColor: String,
  title: String,
  subCaption: String,
  chip: String,
  chipColor: String,
  chipBgColor: String,
  chipVariant: String,
  chipIcon: String,
  level: Number
});
</script>

<template>
    <v-list-item
        :to="type === 'external' ? '' : to"
        :href="type === 'external' ? to : ''"
        class="mb-3"
        :disabled="disabled"
        :target="type === 'external' ? '_blank' : ''">
        <!---If icon-->
        <template v-slot:prepend>
            <Icon :icon="'solar:' + icon" height="20" width="20" :level="level" class="dot" :class="'text-' + BgColor"/>
        </template>
        <v-list-item-title>{{title}}</v-list-item-title>
        <!---If Caption-->
        <v-list-item-subtitle v-if="subCaption" class="text-caption mt-n1 hide-menu">
            {{ subCaption }}
        </v-list-item-subtitle>
        <!---If any chip or label-->
        <template #append v-if="chip">
            <v-chip
                :color="chipColor"
                :class="'sidebarchip hide-menu bg-' + chipBgColor"
                :size="chipIcon ? 'small' : 'small'"
                :variant="chipVariant"
                :prepend-icon="chipIcon"
            >
                {{ chip }}
            </v-chip>
        </template>
    </v-list-item>
</template>
