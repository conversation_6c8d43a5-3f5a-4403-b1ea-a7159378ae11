
import MainRoutes from '@/router/MainRoutes';

export interface menu {
    header?: string;
    title?: string;
    icon?: any;
    to?: string;
    chip?: string;
    BgColor?: string;
    chipBgColor?: string;
    chipColor?: string;
    chipVariant?: string;
    chipIcon?: string;
    children?: menu[];
    disabled?: boolean;
    type?: string;
    subCaption?: string;
}

const sidebarItem: menu[] = MainRoutes.children.map((route: any) => ({
  title: route.meta?.title || route.name,
  to: route.path,
  icon: route.meta?.icon,
  chip: route.meta?.chip,
  BgColor: route.meta?.BgColor,
  chipBgColor: route.meta?.chipBgColor,
  chipColor: route.meta?.chipColor,
  chipVariant: route.meta?.chipVariant,
  chipIcon: route.meta?.chipIcon,
  disabled: route.meta?.disabled,
  subCaption: route.meta?.subCaption,
}));

export default sidebarItem;
