<script setup lang="ts">
import { ref, shallowRef, computed } from 'vue';
import { useRoute } from 'vue-router';
import sidebarItems from './vertical-sidebar/sidebarItem';
import NavGroup from './vertical-sidebar/NavGroup/index.vue';
import NavItem from './vertical-sidebar/NavItem/index.vue';
import Logo from './logo/Logo.vue';
import { useNavigationStore, useAuthStore } from '@/store';

const DRAWER_WIDTH = 256;
const DRAWER_RAIL_IDTH = 56;

const route = useRoute()

const store = useNavigationStore()
const auth = useAuthStore();

const sidebarMenu = shallowRef(sidebarItems);
const profileMenu = ref(false);

const forcedRail = computed(() => route.meta?.rail);
const drawerWidth = computed(() => store.isRail ? DRAWER_RAIL_IDTH : DRAWER_WIDTH)

function toggleRail() {
  store.setRail(!store.isRail);
}
</script>

<template>
  <v-navigation-drawer
    permanent
    class="leftSidebar"
    :rail="forcedRail || store.isRail"
    :width="DRAWER_WIDTH"
    :rail-width="DRAWER_RAIL_IDTH"
  >
    <div :class="[store.isRail ? 'pa-2 mb-12' : 'pa-4']">
        <Logo :collapsed="store.isRail" />
    </div>

    <div class="scrollnavbar">
      <v-list nav>
          <template v-for="(item, i) in sidebarMenu">
              <NavGroup :item="item" v-if="item.header" :key="item.title" />

              <NavItem v-else
                class="leftPadding"
                :title="item.title"
                :to="item.to"
                :disabled="item.disabled"
                :icon="item.icon"
                :BgColor="item.BgColor"
                :subCaption="item.subCaption"
                :chip="item.chip"
                :chipColor="item.chipColor"
                :chipBgColor="item.chipBgColor"
                :chipVariant="item.chipVariant"
                :chipIcon="item.chipIcon"
              />
          </template>
      </v-list>
    </div>

    <template #append>
      <div class="scrollnavbar">
        <v-list nav density="compact" class="px-2">
          <!-- Trial Status -->
          <v-list-item class="mb-2 rounded-lg">
            <template v-slot:prepend>
              <v-icon color="grey-darken-1" size="20">mdi-circle-slice-5</v-icon>
            </template>
            <v-list-item-title class="font-weight-medium">Prueba</v-list-item-title>
            <v-list-item-subtitle class="text-caption text-grey-darken-1">3 / 3 Días</v-list-item-subtitle>
          </v-list-item>

          <!-- Settings -->
          <v-list-item to="/settings" class="mb-2 rounded-lg" title="Ajustes" color="grey-darken-1">
            <template v-slot:prepend>
              <v-icon color="grey-darken-1" size="20">mdi-cog</v-icon>
            </template>
          </v-list-item>

          <!-- User Profile -->
          <v-menu
            v-model="profileMenu"
            :close-on-content-click="false"
            location="top"
            :offset="10"
          >
            <template v-slot:activator="{ props }">
              <v-list-item
                class="mb-2 rounded-lg"
                v-bind="props"
              >
                <template v-slot:prepend>
                  <v-avatar size="20" class="bg-lightprimary">
                    <span class="text-body-2">J</span>
                  </v-avatar>
                </template>
                <v-list-item-title class="text-body-2 text-truncate"><EMAIL></v-list-item-title>
                <template #append>
                  <v-icon color="grey-darken-1">{{profileMenu ? 'mdi-chevron-up' : 'mdi-chevron-down'}}</v-icon>
                </template>
              </v-list-item>
            </template>

            <v-card min-width="300" class="profile-menu-card">
              <!-- Profile Header -->
              <v-list-item inactive>
                <template v-slot:prepend>
                  <v-avatar color="primary" size="40" class="bg-primary text-white">
                    <span class="text-body-1">J</span>
                  </v-avatar>
                </template>
                <v-list-item-title class="font-weight-medium">{{ auth.userDisplayName }}</v-list-item-title>
                <v-list-item-subtitle class="text-caption">{{ auth.userEmail }}</v-list-item-subtitle>
              </v-list-item>

              <v-divider class="my-2"></v-divider>

              <!-- Menu Actions -->
              <div class="scrollnavbar">
                <v-list density="compact">
                  <v-list-item @click="auth.logout" prepend-icon="mdi-logout" title="Cerrar Sesión" />
                </v-list>
              </div>
            </v-card>
          </v-menu>
        </v-list>
      </div>
    </template>
  </v-navigation-drawer>

  <v-btn
    v-if="!forcedRail"
    icon
    size="x-small"
    elevation="4"
    color="white"
    class="rail-toggle-btn"
    @click="toggleRail"
    :style="{ left: `${drawerWidth - 16}px` }"
  >
    <v-icon>{{ store.isRail ? 'mdi-chevron-right' : 'mdi-chevron-left' }}</v-icon>
  </v-btn>
</template>

<style scoped>
.profile-menu-card {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.profile-trigger {
  border: 1px solid transparent;
  transition: background-color 0.2s ease;
}

.profile-trigger:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.rail-toggle-btn {
  position: absolute;
  top: 64px;
  z-index: 2000;
  transition: left 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
  /* Vuetify drawer transition default */
}
</style>
