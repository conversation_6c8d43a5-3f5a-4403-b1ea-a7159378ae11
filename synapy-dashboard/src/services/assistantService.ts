import { functions } from '@/plugins/firebase'
import { httpsCallable } from 'firebase/functions'
import { mapApiResponseToAssistant, mapAssistantToApiRequest } from './mappers/assistantMapper'
import { safeCallable } from './core/firebaseCall'

import type { Assistant } from '@/types/domain'

const createAssistantCallable = httpsCallable(functions, 'create_assistant_http')
const updateAssistantCallable = httpsCallable(functions, 'update_assistant_http')
const deleteAssistantCallable = httpsCallable(functions, 'delete_assistant_http')
const getAssistantDetailsCallable = httpsCallable(functions, 'get_assistant_details_http')
const fetchAssistantsCallable = httpsCallable(functions, 'fetch_assistants_http')

export async function createAssistant(userId: string, assistantType: string, companyName: string): Promise<Assistant | undefined> {
  const data = await safeCallable(createAssistantCallable, {
    user_id: userId,
    assistant_type: assistantType,
    company_name: companyName
  })
  return data && mapApiResponseToAssistant(data)
}

export async function updateAssistant(assistantId: string, assistantData: any): Promise<boolean> {
  const data = await safeCallable<{ status: string }>(updateAssistantCallable, {
    assistant_id: assistantId,
    assistant_data: mapAssistantToApiRequest(assistantData)
  }, d => 'status' in d)
  return data?.status === 'success'
}

export async function deleteAssistant(assistantId: string): Promise<boolean> {
  const data = await safeCallable<{ status: string }>(deleteAssistantCallable, { assistant_id: assistantId }, d => 'status' in d)
  return data?.status === 'success'
}

export async function getAssistantDetails(assistantId: string): Promise<Assistant | undefined> {
  const data = await safeCallable(getAssistantDetailsCallable, { assistant_id: assistantId })
  return data && mapApiResponseToAssistant(data)
}

export async function fetchAssistants(userId: string): Promise<Assistant[] | undefined> {
  const data = await safeCallable(fetchAssistantsCallable, { user_id: userId }, d => Array.isArray(d.assistants))
  return data?.assistants?.map(mapApiResponseToAssistant)
}