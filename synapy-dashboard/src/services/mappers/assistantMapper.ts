import type { Assistant } from '@/types/domain'

export function mapApiResponseToAssistant(data: any): Assistant {
  return {
    id: data.assistant_id ?? '',
    name: data.assistant_name ?? '',
    type: data.assistant_type ?? '',
    timezone: data.time_zone ?? '',
    voiceId: data.voice_id ?? '',
    patienceLevel: mapResponsivenessToPatienceLevel(data.responsiveness),
    stability: Number(data.voice_temperature ?? 0.5),
    voiceSpeed: Number(data.voice_speed ?? 0.5),
    backchannelWords: Array.isArray(data.backchannel_words) ? data.backchannel_words : [],
  }
}

export function mapAssistantToApiRequest(assistant: Assistant): any {
  return {
    assistant_id: assistant.id,
    assistant_type: assistant.type,
    assistant_name: assistant.name,
    time_zone: assistant.timezone,
    voice_id: assistant.voiceId,
    responsiveness: mapPatienceLevelToResponsiveness(assistant.patienceLevel),
    voice_temperature: assistant.stability,
    voice_speed: assistant.voiceSpeed,
    backchannel_words: assistant.backchannelWords,
  }
}

function mapResponsivenessToPatienceLevel(responsiveness: number): string {
  if (responsiveness === undefined || responsiveness === null) return 'medium'
  if (responsiveness < 0.3) return 'low'
  if (responsiveness < 0.7) return 'medium'
  return 'high'
}

function mapPatienceLevelToResponsiveness(patienceLevel?: string): number | null {
  if (!patienceLevel) return null
  switch (patienceLevel) {
    case 'low':
      return 0.2
    case 'medium':
      return 0.5
    case 'high':
      return 0.8
    default:
      return 0.5
  }
}
