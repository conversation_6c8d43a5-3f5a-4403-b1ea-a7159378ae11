import { functions, storage } from '@/plugins/firebase'
import { httpsCallable } from 'firebase/functions'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import type { KnowledgeBase, KnowledgeBaseSource } from '@/types/domain'
import { safeCallable } from './core/firebaseCall'

// Helper types for form data
export interface KnowledgeBaseTextInput {
  text: string;
  title?: string;
}

export interface KnowledgeBaseFormData {
  name: string;
  type: string;
  customTrigger?: string;
  texts?: KnowledgeBaseTextInput[];
  urls?: string[];
  files?: File[];
}

export interface AddSourcesFormData {
  texts?: KnowledgeBaseTextInput[];
  urls?: string[];
  files?: File[];
}

const createKBCallable         = httpsCallable(functions, 'create_knowledge_base_http')
const addKBSourcesCallable     = httpsCallable(functions, 'add_knowledge_base_sources_http')
const deleteKBSourceCallable   = httpsCallable(functions, 'delete_knowledge_base_source_http')
const deleteKBCallable         = httpsCallable(functions, 'delete_knowledge_base_http')
const getKBDetailsCallable     = httpsCallable(functions, 'get_knowledge_base_details_http')
const fetchKBsCallable         = httpsCallable(functions, 'fetch_knowledge_bases_http')

/**
 * Upload files to Firebase Cloud Storage with filename support
 */
async function uploadFilesToStorage(userId: string, files: File[]): Promise<{filename: string, url: string}[]> {
  const uploadPromises = files.map(async (file) => {
    const fileId = crypto.randomUUID()
    const fileExtension = file.name.split('.').pop() || ''
    const storagePath = `knowledge_bases/${userId}/${fileId}.${fileExtension}`

    const storageRef = ref(storage, storagePath)
    const snapshot = await uploadBytes(storageRef, file)
    const downloadURL = await getDownloadURL(snapshot.ref)

    return {
      filename: file.name,
      url: downloadURL
    }
  })

  return Promise.all(uploadPromises)
}

/**
 * Convert form data to domain model with proper sources mapping
 * Note: fileData should be objects with filename and URL from uploaded files
 */
function mapFormDataToDomain(formData: KnowledgeBaseFormData, fileData?: {filename: string, url: string}[]): Partial<KnowledgeBase> {
  const sources: KnowledgeBaseSource[] = []

  // Map text inputs to sources
  if (formData.texts) {
    formData.texts.forEach(textInput => {
      sources.push({
        type: 'text',
        name: textInput.title,
        text: textInput.text,
      })
    })
  }

  // Map URLs to sources
  if (formData.urls) {
    formData.urls.forEach(url => {
      sources.push({
        type: 'url',
        url: url,
      })
    })
  }

  // Map uploaded files to sources with filename support
  if (fileData && fileData.length > 0) {
    fileData.forEach(file => {
      sources.push({
        type: 'document',
        name: file.filename,
        url: file.url,
      })
    })
  }

  return {
    name: formData.name,
    type: formData.type as any,
    customTrigger: formData.customTrigger,
    sources: sources.length > 0 ? sources : undefined
  }
}

export async function createKnowledgeBase(
  userId: string,
  formData: KnowledgeBaseFormData
): Promise<KnowledgeBase | undefined> {
  // Upload files to Cloud Storage first if any
  const fileData = formData.files?.length ? await uploadFilesToStorage(userId, formData.files) : []

  // Map form data to domain model with uploaded file data
  const domainData = mapFormDataToDomain(formData, fileData)

  const payload: any = {
    user_id: userId,
    knowledge_base_name: domainData.name,
    knowledge_base_type: domainData.type,
    custom_trigger: domainData.customTrigger,
  }

  // Extract different source types for the backend
  if (domainData.sources) {
    const texts = domainData.sources.filter(s => s.type === 'text').map(s => ({ text: s.text!, title: s.name }))
    const urls = domainData.sources.filter(s => s.type === 'url').map(s => s.url!)
    const fileData = domainData.sources.filter(s => s.type === 'document').map(s => ({ filename: s.name!, url: s.url! }))

    if (texts.length > 0) payload.knowledge_base_texts = texts
    if (urls.length > 0) payload.knowledge_base_urls = urls
    if (fileData.length > 0) payload.knowledge_base_file_urls = fileData
  }

  const data = await safeCallable(createKBCallable, payload)
  console.debug('KB created:', data)
  return data && mapApiResponseToKnowledgeBase(data)
}

/**
 * Helper function to map AddSourcesFormData to domain sources
 */
function mapAddSourcesFormDataToDomain(formData: AddSourcesFormData, fileData?: {filename: string, url: string}[]): KnowledgeBaseSource[] {
  const sources: KnowledgeBaseSource[] = []

  // Map text inputs to sources
  if (formData.texts) {
    formData.texts.forEach(textInput => {
      sources.push({
        type: 'text',
        name: textInput.title,
        text: textInput.text,
      })
    })
  }

  // Map URLs to sources
  if (formData.urls) {
    formData.urls.forEach(url => {
      sources.push({
        type: 'url',
        url: url,
      })
    })
  }

  // Map uploaded files to sources with filename support
  if (fileData && fileData.length > 0) {
    fileData.forEach(file => {
      sources.push({
        type: 'document',
        name: file.filename,
        url: file.url,
      })
    })
  }

  return sources
}

export async function addKnowledgeBaseSources(
  userId: string,
  knowledgeBaseId: string,
  formData: AddSourcesFormData
): Promise<KnowledgeBase | undefined> {
  // Upload files to Cloud Storage first if any
  const fileData = formData.files?.length ? await uploadFilesToStorage(userId, formData.files) : []

  // Map form data to domain sources
  const sources = mapAddSourcesFormDataToDomain(formData, fileData)

  // Convert sources to the format expected by the backend
  const texts = sources.filter(s => s.type === 'text').map(s => ({ text: s.text!, title: s.name }))
  const urls = sources.filter(s => s.type === 'url').map(s => s.url!)
  const fileData_backend = sources.filter(s => s.type === 'document').map(s => ({ filename: s.name!, url: s.url! }))

  const payload: any = {
    knowledge_base_id: knowledgeBaseId,
  }

  if (texts.length > 0) payload.knowledge_base_texts = texts
  if (urls.length > 0) payload.knowledge_base_urls = urls
  if (fileData_backend.length > 0) payload.knowledge_base_file_urls = fileData_backend

  const data = await safeCallable(addKBSourcesCallable, payload)
  return data && mapApiResponseToKnowledgeBase(data)
}

export async function deleteKnowledgeBaseSource(
  knowledgeBaseId: string,
  sourceId: string
): Promise<KnowledgeBase | undefined> {
  const data = await safeCallable(deleteKBSourceCallable, {
    knowledge_base_id: knowledgeBaseId,
    source_id: sourceId,
  })
  return data && mapApiResponseToKnowledgeBase(data)
}

export async function deleteKnowledgeBase(kbId: string): Promise<boolean> {
  const data = await safeCallable<{ status: string }>(deleteKBCallable, { knowledge_base_id: kbId }, d => 'status' in d)
  return data?.status === 'success'
}

export async function getKnowledgeBaseDetails(kbId: string): Promise<KnowledgeBase | undefined> {
  const data = await safeCallable(getKBDetailsCallable, { knowledge_base_id: kbId })
  return data && mapApiResponseToKnowledgeBase(data)
}

export async function fetchKnowledgeBases(userId: string): Promise<KnowledgeBase[] | undefined> {
  const data = await safeCallable(fetchKBsCallable, { user_id: userId }, d => Array.isArray(d.knowledge_bases))
  return data?.knowledge_bases?.map(mapApiResponseToKnowledgeBase)
}

function mapApiResponseToKnowledgeBase(apiData: any): KnowledgeBase {
  return {
    id: apiData.knowledge_base_id,
    name: apiData.knowledge_base_name,
    status: apiData.status,
    sources: mapApiSourcesToKnowledgeBaseSources(apiData.sources),
    type: apiData.knowledge_base_type,
    customTrigger: apiData.custom_trigger,
  }
}

function mapApiSourcesToKnowledgeBaseSources(apiSources: any[]): KnowledgeBaseSource[] {
  return apiSources.map((source: any) => ({
    type: source.type,
    sourceId: source.source_id,
    name: source.name,
    url: source.url,
    text: source.text,
  }))
}
