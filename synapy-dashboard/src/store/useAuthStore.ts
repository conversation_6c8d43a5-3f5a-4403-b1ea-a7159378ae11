import { defineStore } from "pinia"
import { ref, computed } from "vue"
import { useRouter } from 'vue-router';
import { auth } from "@/plugins/firebase"
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
} from "firebase/auth"
import type { User } from "firebase/auth"

export const useAuthStore = defineStore("auth", () => {
  const user = ref<User | null>(null)
  const isLoading = ref(true)
  const router = useRouter();

  const initAuthListener = () => {
    onAuthStateChanged(auth, (firebaseUser) => {
      user.value = firebaseUser;
      console.debug('User state changed:', firebaseUser);
      isLoading.value = false;
    })
  }

  const userEmail = computed(() => user.value?.email ?? '');
  const userDisplayName = computed(() => user.value?.displayName ?? '');

  const register = (email: string, password: string) =>
    createUserWithEmailAndPassword(auth, email, password)

  const login = (email: string, password: string) =>
    signInWithEmailAndPassword(auth, email, password)

  async function logout() {
    try {
      await signOut(auth);
      router.push({ name: 'login' });
    } catch (error) {
      console.error('❌ Logout error:', error);
    }
  }

  return {
    user,
    userEmail,
    userDisplayName,
    isLoading,
    initAuthListener,
    register,
    login,
    logout,
    isAuthenticated: computed(() => user.value !== null),
  }
})
