/*This is for the logo*/
.leftSidebar {
    box-shadow: none !important;

    .logo {
        padding-left: 7px;
    }

    .mini-icon {
        display: none;
    }

    .mini-text {
        display: block;
        font-size: 12px !important;
        font-weight: 600 !important;
        letter-spacing: 1.5px !important;
        text-transform: uppercase !important;
        color:inherit
    }

    .v-list--density-default .v-list-subheader {
        padding-inline-start: 0 !important;
    }


}

/*This is for the Vertical sidebar*/
.scrollnavbar {
    .smallCap {
        padding: 20px 0px 10px !important;
        font-size: 0.875rem;
        font-weight: 500;
        margin-top: 24px;
        color: rgb(var(--v-theme-textPrimary));

        &:first-child {
            margin-top: 0 !important;
        }
    }

    /*General Menu css*/
    .v-list-group__items {
        .v-list-item {
            min-height: 35px !important;
            padding-inline-start: calc(12px + var(--indent-padding) / 10) !important;

            .v-list-item__prepend .dot {
                height: 6px;
                width: 6px;
                background-color: rgb(var(--v-theme-textSecondary));
                border-radius: 50%;
                margin-inline-end: 8px !important;
            }

            .v-list-item-title {
                font-size: 14px !important;
            }

            &:hover {
                color: rgb(var(--v-theme-primary));

                .v-list-item__prepend .dot {
                    background-color: rgb(var(--v-theme-primary));
                }
            }

            &.v-list-item--active {
                .v-list-item__prepend .dot {
                    background-color: rgb(var(--v-theme-primary));
                }
            }

        }
    }

    .v-list-group__items .v-list-item,
    .v-list-item {
        border-radius: $border-radius-root + 4;
        color: rgba(var(--v-theme-textPrimary),0.8);
        margin: 0 0 2px;

        &:hover {
            color: rgb(var(--v-theme-primary));
        }

        .v-list-item__prepend {
            margin-inline-end: 13px;
        }

        .v-list-item__append {
            font-size: 15px;

            .v-icon {
                margin-inline-start: 13px;
            }
        }

        .v-list-item-title {
            font-size: 15px;

        }
    }

    /*This is for the dropdown*/
    .v-list {
        color: rgb(var(--v-theme-textSecondary));

        .v-list-subheader {
            &:first-of-type {
                border-top: 0;
            }

            border-top: 1px solid rgb(var(--v-theme-borderColor));
        }


        >.v-list-item.v-list-item--active,
        .v-list-item--active>.v-list-item__overlay {
            background: rgb(var(--v-theme-primary));
            color: white;
        }

        >.v-list-group {
            position: relative;

            >.v-list-item--active,
            >.v-list-item--active:hover {
                background: rgb(var(--v-theme-primary));
                color: white;
            }

            .v-list-group__items .v-list-item.v-list-item--active,
            .v-list-group__items .v-list-item.v-list-item--active>.v-list-item__overlay {
                background: transparent;
                color: rgb(var(--v-theme-primary));
            }
        }
    }
}

.v-list-item--density-default:not(.v-list-item--nav).v-list-item--one-line {
    padding-inline: 14px;
}

.v-navigation-drawer--rail {

    .scrollnavbar .v-list .v-list-group__items,
    .hide-menu {
        opacity: 1;
    }


    .leftPadding {
        margin-left: 0px;
    }
}


// scrollbar
.ps__rail-y {
    z-index: 9;
}
