import type { Call<PERSON><PERSON><PERSON>, PhoneN<PERSON><PERSON> } from '@/types/domain';

const PhoneNumbersData: PhoneNumber[] = [
  // {
  //   number: "+34902202122",
  //   label: "Main number",
  //   type: "Main",
  //   active: true,
  //   inboundAssistant: {
  //     id: "asdf123",
  //     name: "Asistente 1",
  //   },
  // },
  // {
  //   number: "+34902202123",
  //   label: "Support number",
  //   type: "Support",
  //   active: true,
  // },
  // {
  //   number: "+34902202124",
  //   label: "Sales number",
  //   type: "Sales",
  //   active: true,
  //   outboundAssistant: {
  //     id: "asdf123",
  //     name: "Asistente 1",
  //   },
  // }
]

const CallRecordData: CallRecord[] = [
  {
    datetime: new Date(),
    durationSeconds: 130,
    from: "+34902202122",
    callId: "adf123",
  },
  {
    datetime: new Date(),
    durationSeconds: 15,
    from: "+34902202122",
    callId: "adf124",
  },
  {
    datetime: new Date(),
    durationSeconds: 345,
    from: "+34902202122",
    callId: "adf125",
  },
  {
    datetime: new Date(),
    durationSeconds: 1024,
    from: "+34902202122",
    callId: "adf126",
  },
  {
    datetime: new Date(),
    durationSeconds: 97,
    from: "+34902202122",
    callId: "adf127",
  },
];

export {
  CallRecordData,
  PhoneNumbersData,
};
