{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"runtime": "python311", "source": "functions", "codebase": "default", "ignore": ["venv", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}], "hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true}, "singleProjectMode": true, "storage": {"port": 9199}}, "remoteconfig": {"template": "remoteconfig.template.json"}, "storage": {"rules": "storage.rules"}}