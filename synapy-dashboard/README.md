# 🧠 Synapy Dashboard

Administration panel for the **Synapy** platform, built with Vue 3 + TypeScript, Firebase, and automated deployment through GitHub Actions.

---

## 🚀 Main Technologies

- ⚙️ [Vue 3](https://vuejs.org/)
- 🔥 [Firebase (Auth, Firestore, Functions)](https://firebase.google.com/)
- 💻 GitHub Actions for CI/CD
- 📦 Node.js 22.x

---

## 📦 Project Initialization

```bash
# Clone the repo
git clone https://github.com/synapse-sea/synapy-dashboard.git
cd synapy-dashboard

# Install dependencies
npm install
```

---

## 🔧 Environment Setup

Create a `.env` file in the project root with the following variables:

```env
VITE_FIREBASE_API_KEY=...
VITE_FIREBASE_PROJECT_ID=synapy
VITE_FIREBASE_MESSAGING_SENDER_ID=...
VITE_FIREBASE_APP_ID=...
VITE_FIREBASE_MEASUREMENT_ID=...
```

These variables must match your Firebase project credentials.

---

## 🔐 Firebase Authentication Configuration

- Email/Password and Google Sign-In must be enabled.
- Make sure to properly configure authorized origins in Firebase Console → Auth → Settings.

---

## 📂 Project Structure

```
├── src/
│   ├── components/
│   ├── views/
│   ├── router/
│   ├── store/
│   └── firebase/
├── functions/
├── dist/
├── .env
└── firebase.json
```

---

## 📥 Firebase Emulators (local development)

1. Install Firebase CLI:
```bash
npm install -g firebase-tools
```

2. Start the emulators:
```bash
firebase emulators:start
```

This will start Auth, Firestore, and Functions locally.

> Make sure that `firebase.json` and `.firebaserc` are properly configured to use the emulators.

---

## 🧪 Run Locally

```bash
# Start the frontend
npm run dev

# In parallel, start Firebase emulators
firebase emulators:start
```

---

## ☁️ Production Deployment

Deployment happens automatically when pushing to `main`, using GitHub Actions and federated authentication with Google Cloud. Follow these steps to configure everything correctly.

This project uses an automated GitHub Actions workflow to deploy Firebase Functions upon pushing to the main branch. The diagram below illustrates the entire flow, from code changes to live deployment in the cloud.


🔧 Built with GitHub Actions, Firebase CLI, and Google Cloud Functions (2nd gen).
Ensures consistency, automation, and production-grade deployment with every push.

![Automatic Deployment Flow](./docs/assets/deployment-flow.png)

---

## 🔐 Required Google Cloud APIs & Config

Make sure the following Google Cloud APIs are **enabled** in your project (`synapy`):

- Identity and Access Management (IAM) API: `iam.googleapis.com`
- IAM Credentials API: `iamcredentials.googleapis.com`
- Cloud Resource Manager API: `cloudresourcemanager.googleapis.com`
- Firebase Management API: `firebase.googleapis.com`
- Cloud Functions API: `cloudfunctions.googleapis.com`
- Firestore API: `firestore.googleapis.com`
- Cloud Build API: `cloudbuild.googleapis.com`
- Service Usage API: `serviceusage.googleapis.com`

Enable them via Cloud Console or CLI:

```bash
gcloud services enable iam.googleapis.com iamcredentials.googleapis.com cloudresourcemanager.googleapis.com firebase.googleapis.com cloudfunctions.googleapis.com firestore.googleapis.com cloudbuild.googleapis.com serviceusage.googleapis.com --project=synapy
```

---

## 🛡️ Additional GCP Service Account Notes

- Ensure that the GitHub Actions service account (`github-deployer@...`) has **Service Account Token Creator**, **Firebase Admin**, and **Cloud Functions Admin** roles.
- Additionally, grant it `Service Account User` (roles/iam.serviceAccountUser) on the `appspot.gserviceaccount.com` default runtime account for function deployment.

```bash
gcloud iam service-accounts add-iam-policy-binding <EMAIL> --role="roles/iam.serviceAccountUser" --member="serviceAccount:<EMAIL>"
```

---

##

---

## 🧼 Linter & Formatting

This project includes:

```bash
npm run lint         # ESLint
npm run format       # Prettier
```

---

## Troubleshooting

1. Error: Missing permissions required for functions deploy. You must have permission iam.serviceAccounts.actAs on service account <PROJECT_ID>@appspot.gserviceaccount.com

```
🔧 Step-by-step solution
Go to the IAM & Admin Console → Service Accounts
https://console.cloud.google.com/iam-admin/serviceaccounts?project=<PROJECT_ID>

Look for the service account <PROJECT_ID>@appspot.gserviceaccount.com (the default one used by Cloud Functions).

Edit IAM bindings for that Service Account
In the “Permissions” (or “Principals” tab, depending on your UI language), click on “+ ADD PRINCIPAL”.

Add your GitHub Actions account

New principal: serviceAccount:github-deployer@<PROJECT_ID>.iam.gserviceaccount.com

Role: Service Account User (roles/iam.serviceAccountUser)

This grants permission to impersonate (actAs) that service account.
```

---

## 📚 Additional Resources

- [Vue 3 Docs](https://vuejs.org/)
- [Firebase Docs](https://firebase.google.com/docs)

---

## 🧠 Author

**Juan Carlos González Cabrero**
Agency: [Synapse Sea](https://synapsesea.com)
