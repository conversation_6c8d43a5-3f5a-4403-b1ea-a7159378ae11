"""
Firebase Functions Entry Point

This module serves as the entry point for all Firebase Functions.
It imports and exposes all function definitions from the organized modules.

The functions are organized by domain (assistant, knowledge_base) and follow
the hexagonal architecture pattern with proper separation of concerns.
"""

from src.shared import initialize

# Import all Firebase Functions from organized modules
from src.app.functions import (
    # Assistant functions
    create_assistant_http,
    update_assistant_http,
    get_assistant_details_http,
    delete_assistant_http,
    fetch_assistants_http,

    # Knowledge base functions
    create_knowledge_base_http,
    add_knowledge_base_sources_http,
    delete_knowledge_base_source_http,
    get_knowledge_base_details_http,
    delete_knowledge_base_http,
    fetch_knowledge_bases_http,
)

# Initialize the application
initialize()

# Export all functions so Firebase can discover them
__all__ = [
    # Assistant functions
    "create_assistant_http",
    "update_assistant_http",
    "get_assistant_details_http",
    "delete_assistant_http",
    "fetch_assistants_http",

    # Knowledge base functions
    "create_knowledge_base_http",
    "add_knowledge_base_sources_http",
    "delete_knowledge_base_source_http",
    "get_knowledge_base_details_http",
    "delete_knowledge_base_http",
    "fetch_knowledge_bases_http",
]
