import json
import traceback
from functools import wraps
from firebase_functions.https_fn import CallableRequest
from .logger import Logger

def log_callable_calls(func):
    @wraps(func)
    def wrapper(request: CallableRequest, *args, **kwargs):
        function_name = func.__name__
        try:
            Logger.info(f"📥 Calling function [{function_name}] with data: {request.data}")
            Logger.debug(f"Auth context: {request.auth}")
        except Exception as e:
            Logger.warn(f"⚠️ [{function_name}] Failed to log request info: {e}")

        try:
            response = func(request, *args, **kwargs)
            Logger.info(f"✅ [{function_name}] Response: {json.dumps(response)}")
            return response
        except Exception as e:
            Logger.error(f"❌ [{function_name}] {e}")
            Logger.debug(f"Stack Trace: {traceback.format_exc()}")
            raise e
    return wrapper
