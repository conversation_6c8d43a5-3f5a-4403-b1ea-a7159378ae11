"""
Dependency Injection Container for the application.
This module provides a simple service container for managing dependencies.
"""

from typing import Dict, Type, TypeVar, Callable, Any
from src.domain.services import AssistantService, KnowledgeBaseService
from src.infrastructure.firebase import (
    FirestoreAssistantRepository,
    FirebaseStorageRepository,
    FirestoreKnowledgeBaseRepository,
)
from src.infrastructure.retell import (
    RetellAssistantRepository,
    RetellKnowledgeBaseRepository,
)

T = TypeVar('T')

class ServiceContainer:
    """Simple dependency injection container."""

    def __init__(self):
        self._services: Dict[Type, Any] = {}
        self._factories: Dict[Type, Callable[[], Any]] = {}
        self._setup_default_services()

    def _setup_default_services(self):
        """Setup default service factories."""
        # Infrastructure repositories
        self.register_factory(FirestoreAssistantRepository, lambda: FirestoreAssistantRepository())
        self.register_factory(FirebaseStorageRepository, lambda: FirebaseStorageRepository())
        self.register_factory(RetellAssistantRepository, lambda: RetellAssistantRepository())
        self.register_factory(FirestoreKnowledgeBaseRepository, lambda: FirestoreKnowledgeBaseRepository())
        self.register_factory(RetellKnowledgeBaseRepository, lambda: RetellKnowledgeBaseRepository())

        # Domain services
        self.register_factory(AssistantService, lambda: AssistantService())
        self.register_factory(KnowledgeBaseService, lambda: KnowledgeBaseService())

    def register_factory(self, service_type: Type[T], factory: Callable[[], T]):
        """Register a factory function for a service type."""
        self._factories[service_type] = factory

    def register_instance(self, service_type: Type[T], instance: T):
        """Register a singleton instance for a service type."""
        self._services[service_type] = instance

    def get(self, service_type: Type[T]) -> T:
        """Get a service instance."""
        # Return singleton if exists
        if service_type in self._services:
            return self._services[service_type]

        # Create from factory if available
        if service_type in self._factories:
            instance = self._factories[service_type]()
            self._services[service_type] = instance  # Cache as singleton
            return instance

        raise ValueError(f"Service {service_type.__name__} not registered")

    def clear(self):
        """Clear all cached services (useful for testing)."""
        self._services.clear()

# Global container instance
_container = ServiceContainer()

def get_service(service_type: Type[T]) -> T:
    """Get a service from the global container."""
    return _container.get(service_type)

def register_service_factory(service_type: Type[T], factory: Callable[[], T]):
    """Register a service factory in the global container."""
    _container.register_factory(service_type, factory)

def register_service_instance(service_type: Type[T], instance: T):
    """Register a service instance in the global container."""
    _container.register_instance(service_type, instance)
