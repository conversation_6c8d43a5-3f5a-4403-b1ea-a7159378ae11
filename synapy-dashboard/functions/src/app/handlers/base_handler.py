"""
Base handler class providing common functionality for all handlers.
"""

from typing import Type, TypeVar, Dict, Any
from src.shared.container import get_service
from src.domain.exceptions import BadRequestError

T = TypeVar('T')

class BaseHandler:
    """Base class for all application handlers."""

    def get_service(self, service_type: Type[T]) -> T:
        """Get a service instance from the container."""
        return get_service(service_type)

    def validate_request(self, request_model_class: Type[T], request_data: Dict[str, Any]) -> T:
        """Validate and parse request data into a request model."""
        try:
            return request_model_class(**request_data)
        except Exception as e:
            raise BadRequestError(f"Invalid request data: {str(e)}")

    def create_response(self, response_model_class: Type[T], domain_object: Any) -> Dict[str, Any]:
        """Create a response from a domain object."""
        response = response_model_class.from_domain(domain_object)
        return response.to_dict()
