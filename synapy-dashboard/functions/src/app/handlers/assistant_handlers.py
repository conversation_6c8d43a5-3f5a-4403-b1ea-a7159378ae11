"""
Handler class for Assistant operations using dependency injection.
"""

from typing import Dict, Any
from .base_handler import <PERSON><PERSON><PERSON><PERSON>
from src.domain.services import AssistantService
from src.app.models import AssistantResponse
from src.app.models.requests.assistant import (
    CreateAssistantRequest,
    UpdateAssistantRequest,
    GetAssistantRequest,
    DeleteAssistantRequest,
    FetchAssistantsRequest,
)

class AssistantHandlers(BaseHandler):
    """Handlers for assistant operations."""

    def create_assistant(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle create assistant request."""
        dto = self.validate_request(CreateAssistantRequest, request_data)
        service = self.get_service(AssistantService)
        assistant = service.create(dto.to_domain())
        return self.create_response(AssistantResponse, assistant)

    def update_assistant(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle update assistant request."""
        dto = self.validate_request(UpdateAssistantRequest, request_data)
        service = self.get_service(AssistantService)
        service.update(dto.assistant_id, dto.to_domain())
        return {"success": True}

    def get_assistant_details(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle get assistant details request."""
        dto = self.validate_request(GetAssistantRequest, request_data)
        service = self.get_service(AssistantService)
        assistant = service.get(dto.assistant_id)
        return self.create_response(AssistantResponse, assistant)

    def delete_assistant(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle delete assistant request."""
        dto = self.validate_request(DeleteAssistantRequest, request_data)
        service = self.get_service(AssistantService)
        service.delete(dto.assistant_id)
        return {"success": True}

    def fetch_assistants(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle fetch assistants request."""
        dto = self.validate_request(FetchAssistantsRequest, request_data)
        service = self.get_service(AssistantService)
        assistants = service.fetch_by_user(dto.user_id)
        return {
            "assistants": [AssistantResponse.from_domain(assistant).to_dict() for assistant in assistants]
        }
