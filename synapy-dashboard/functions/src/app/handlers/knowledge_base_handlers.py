"""
Handler class for Knowledge Base operations using dependency injection.
"""

from typing import Dict, Any
from .base_handler import <PERSON><PERSON><PERSON><PERSON>
from src.domain.services import KnowledgeBaseService
from src.app.models import KnowledgeBaseResponse
from src.app.models.requests.knowledge_base import (
    CreateKnowledgeBaseRequest,
    AddKnowledgeBaseSourcesRequest,
    DeleteKnowledgeBaseSourceRequest,
    GetKnowledgeBaseRequest,
    DeleteKnowledgeBaseRequest,
    FetchKnowledgeBasesRequest,
)

class KnowledgeBaseHandlers(BaseHandler):
    """Handlers for knowledge base operations."""

    def create_knowledge_base(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle create knowledge base request."""
        dto = self.validate_request(CreateKnowledgeBaseRequest, request_data)
        service = self.get_service(KnowledgeBaseService)
        knowledge_base = service.create(dto.to_domain())
        return self.create_response(KnowledgeBaseResponse, knowledge_base)

    def add_knowledge_base_sources(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle add knowledge base sources request."""
        dto = self.validate_request(AddKnowledgeBaseSourcesRequest, request_data)
        sources = dto.to_domain_sources()
        service = self.get_service(KnowledgeBaseService)
        updated_kb = service.add_sources(dto.knowledge_base_id, sources)
        return self.create_response(KnowledgeBaseResponse, updated_kb)

    def delete_knowledge_base_source(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle delete knowledge base source request."""
        dto = self.validate_request(DeleteKnowledgeBaseSourceRequest, request_data)
        service = self.get_service(KnowledgeBaseService)
        updated_kb = service.delete_source(dto.knowledge_base_id, dto.source_id)
        return self.create_response(KnowledgeBaseResponse, updated_kb)

    def get_knowledge_base_details(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle get knowledge base details request."""
        dto = self.validate_request(GetKnowledgeBaseRequest, request_data)
        service = self.get_service(KnowledgeBaseService)
        knowledge_base = service.get(dto.knowledge_base_id)
        return self.create_response(KnowledgeBaseResponse, knowledge_base)

    def delete_knowledge_base(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle delete knowledge base request."""
        dto = self.validate_request(DeleteKnowledgeBaseRequest, request_data)
        service = self.get_service(KnowledgeBaseService)
        service.delete(dto.knowledge_base_id)
        return {"success": True}

    def fetch_knowledge_bases(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle fetch knowledge bases request."""
        dto = self.validate_request(FetchKnowledgeBasesRequest, request_data)
        service = self.get_service(KnowledgeBaseService)
        knowledge_bases = service.fetch_by_user(dto.user_id)
        return {
            "knowledge_bases": [KnowledgeBaseResponse.from_domain(kb).to_dict() for kb in knowledge_bases]
        }
