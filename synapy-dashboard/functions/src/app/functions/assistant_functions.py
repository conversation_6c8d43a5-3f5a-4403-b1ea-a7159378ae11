"""
Firebase Functions for Assistant operations.
This module defines the HTTP endpoints for assistant-related operations.
"""

from src.infrastructure.http import authenticated_firebase_function
from src.app.handlers import AssistantHandlers

# Create handler instance
_assistant_handlers = AssistantHandlers()

@authenticated_firebase_function
def create_assistant_http(request_data: dict) -> dict:
    """Create a new assistant."""
    return _assistant_handlers.create_assistant(request_data)

@authenticated_firebase_function
def update_assistant_http(request_data: dict) -> dict:
    """Update an existing assistant."""
    return _assistant_handlers.update_assistant(request_data)

@authenticated_firebase_function
def get_assistant_details_http(request_data: dict) -> dict:
    """Get assistant details by ID."""
    return _assistant_handlers.get_assistant_details(request_data)

@authenticated_firebase_function
def delete_assistant_http(request_data: dict) -> dict:
    """Delete an assistant by ID."""
    return _assistant_handlers.delete_assistant(request_data)

@authenticated_firebase_function
def fetch_assistants_http(request_data: dict) -> dict:
    """Fetch all assistants for a user."""
    return _assistant_handlers.fetch_assistants(request_data)
