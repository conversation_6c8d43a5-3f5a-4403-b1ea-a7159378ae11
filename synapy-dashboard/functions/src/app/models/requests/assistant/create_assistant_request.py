from src.domain.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Assistant, BusinessConfig

class CreateAssistantRequest(SerializableModel):
    user_id: str
    assistant_type: str
    company_name: str

    def to_domain(self) -> Assistant:
        return Assistant(
            user_id         = self.user_id,
            type            = self.assistant_type,
            business_config = BusinessConfig(
                company_name = self.company_name,
            ),
        )
