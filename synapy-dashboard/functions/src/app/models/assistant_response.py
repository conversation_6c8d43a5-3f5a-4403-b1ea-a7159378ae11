from typing import Optional, List
from src.domain.models import SerializableModel, Assistant, BusinessConfig, LLMConfig, AgentConfig

class AssistantResponse(SerializableModel):
    assistant_id: Optional[str] = None
    assistant_type: Optional[str] = None
    user_id: Optional[str] = None
    external_agent_id: Optional[str] = None
    external_llm_id: Optional[str] = None

    assistant_name: Optional[str] = None
    company_name: Optional[str] = None
    time_zone: Optional[str] = None
    notice_hours: Optional[int] = None
    phone_prefix: Optional[str] = None
    time_slot_options: Optional[int] = None
    time_slot_interval: Optional[int] = None
    support_email: Optional[str] = None
    support_number: Optional[str] = None

    model_temperature: Optional[float] = None
    begin_message: Optional[str] = None
    knowledge_base_ids: Optional[List[str]] = None

    voice_temperature: Optional[float] = None
    voice_speed: Optional[float] = None
    responsiveness: Optional[float] = None
    backchannel_words: Optional[List[str]] = None
    ambient_sound: Optional[str] = None
    language: Optional[str] = None
    voice_id: Optional[str] = None

    def to_domain(self) -> Assistant:
        return Assistant(
            id              = self.assistant_id,
            user_id         = self.user_id,
            type            = self.assistant_type,
            agent_id        = self.external_agent_id,
            llm_id          = self.external_llm_id,
            business_config = BusinessConfig(
                assistant_name      = self.assistant_name,
                company_name        = self.company_name,
                time_zone           = self.time_zone,
                notice_hours        = self.notice_hours,
                phone_prefix        = self.phone_prefix,
                time_slot_options   = self.time_slot_options,
                time_slot_interval  = self.time_slot_interval,
                support_email       = self.support_email,
                support_number      = self.support_number,
            ),
            llm_config      = LLMConfig(
                model_temperature   = self.model_temperature,
                begin_message       = self.begin_message,
                knowledge_base_ids  = self.knowledge_base_ids,
            ),
            agent_config    = AgentConfig(
                voice_temperature = self.voice_temperature,
                voice_speed       = self.voice_speed,
                responsiveness    = self.responsiveness,
                backchannel_words = self.backchannel_words,
                ambient_sound     = self.ambient_sound,
                language          = self.language,
                voice_id          = self.voice_id,
            )
        )

    @classmethod
    def from_domain(cls, assistant: Assistant) -> "AssistantResponse":
        return cls(
            assistant_id        = assistant.id,
            user_id             = assistant.user_id,
            external_agent_id   = assistant.agent_id,
            assistant_type      = assistant.type,
            external_llm_id     = assistant.llm_id,

            assistant_name      = getattr(assistant.business_config, "assistant_name", None),
            company_name        = getattr(assistant.business_config, "company_name", None),
            time_zone           = getattr(assistant.business_config, "time_zone", None),
            notice_hours        = getattr(assistant.business_config, "notice_hours", None),
            phone_prefix        = getattr(assistant.business_config, "phone_prefix", None),
            time_slot_options   = getattr(assistant.business_config, "time_slot_options", None),
            time_slot_interval  = getattr(assistant.business_config, "time_slot_interval", None),
            support_email       = getattr(assistant.business_config, "support_email", None),
            support_number      = getattr(assistant.business_config, "support_number", None),

            model_temperature   = getattr(assistant.llm_config, "model_temperature", None),
            begin_message       = getattr(assistant.llm_config, "begin_message", None),
            knowledge_base_ids  = getattr(assistant.llm_config, "knowledge_base_ids", None),

            voice_temperature   = getattr(assistant.agent_config, "voice_temperature", None),
            voice_speed         = getattr(assistant.agent_config, "voice_speed", None),
            responsiveness      = getattr(assistant.agent_config, "responsiveness", None),
            backchannel_words   = getattr(assistant.agent_config, "backchannel_words", None),
            ambient_sound       = getattr(assistant.agent_config, "ambient_sound", None),
            language            = getattr(assistant.agent_config, "language", None),
            voice_id            = getattr(assistant.agent_config, "voice_id", None),
        )
