"""
Base Firestore client providing common functionality for all domain-specific clients.
"""

import json
from firebase_admin import firestore
from typing import List, TypeVar, Generic, Type
from abc import ABC
from .exceptions import FirestoreError

T = TypeVar('T')

class BaseFirestoreClient(ABC, Generic[T]):
    """Base class for all Firestore clients."""

    def __init__(self, collection_name: str, model_class: Type[T]):
        self.collection_name = collection_name
        self.model_class = model_class

    def add(self, data: T) -> str:
        """Create a new document in Firestore."""
        try:
            db = firestore.client()
            payload = data.to_dict()
            collection = db.collection(self.collection_name)
            document = collection.document()
            document.set({**payload, "createdAt": firestore.SERVER_TIMESTAMP})
            return document.id
        except Exception as e:
            raise FirestoreError(f"Error creating {self.collection_name[:-1]} in Firestore: {e}")

    def update(self, id: str, data: T) -> None:
        """Update an existing document in Firestore."""
        try:
            db = firestore.client()
            payload = data.to_dict()
            collection = db.collection(self.collection_name)
            document = collection.document(id)
            document.update({**payload, "updatedAt": firestore.SERVER_TIMESTAMP})
        except Exception as e:
            raise FirestoreError(f"Error updating {self.collection_name[:-1]} in Firestore: {e}")

    def get(self, id: str) -> T:
        """Retrieve a document from Firestore."""
        try:
            db = firestore.client()
            collection = db.collection(self.collection_name)
            document = collection.document(id)
            data = document.get()
            if data.exists:
                firestore_data = data.to_dict()
                firestore_data['id'] = data.id
                return self.model_class(**firestore_data)
            else:
                raise FirestoreError(f"{self.collection_name[:-1].title()} with ID {id} does not exist.")
        except Exception as e:
            if isinstance(e, FirestoreError):
                raise e
            raise FirestoreError(f"Error retrieving {self.collection_name[:-1]} from Firestore: {e}")

    def find(self, filters: dict) -> List[T]:
        """Retrieve documents from Firestore based on provided filters."""
        try:
            db = firestore.client()
            collection_ref = db.collection(self.collection_name)
            query = collection_ref

            for field, value in filters.items():
                query = query.where(field, "==", value)
            docs = query.stream()

            results = []
            for doc in docs:
                data = doc.to_dict()
                if data:
                    data['id'] = doc.id
                    results.append(self.model_class(**data))

            return results
        except Exception as e:
            raise FirestoreError(f"Error retrieving {self.collection_name} with filters {json.dumps(filters)}: {e}")

    def delete(self, id: str) -> None:
        """Delete a document from Firestore."""
        try:
            db = firestore.client()
            collection = db.collection(self.collection_name)
            document = collection.document(id)
            document.delete()
        except Exception as e:
            raise FirestoreError(f"Error deleting {self.collection_name[:-1]} from Firestore: {e}")
