from src.domain.exceptions import AppError

class FirestoreError(AppError):
    def __init__(self, message: str, cause: Exception | None = None):
        super().__init__(message, code="firestore_error", cause=cause)
        self.code = "firestore_error"

class FirestoreWriteError(FirestoreError):
    def __init__(self, document_id: str, cause: Exception | None = None):
        super().__init__(f"Failed to write document {document_id} to Firestore", cause=cause)

class FirestoreReadError(FirestoreError):
    def __init__(self, document_id: str, cause: Exception | None = None):
        super().__init__(f"Failed to read document {document_id} from Firestore", cause=cause)

class StorageError(AppError):
    def __init__(self, message: str, cause: Exception | None = None):
        super().__init__(message, code="storage_error", cause=cause)
        self.code = "storage_error"

class StorageReadError(StorageError):
    def __init__(self, file_path: str, cause: Exception | None = None):
        super().__init__(f"Failed to read file {file_path} from Firebase Storage", cause=cause)
