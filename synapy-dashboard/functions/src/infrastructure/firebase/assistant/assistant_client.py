from typing import List
from ..shared import BaseFirestoreClient
from .assistant_models import FirestoreAssistantData

COLLECTION = 'assistants'

class FirestoreAssistantClient(BaseFirestoreClient[FirestoreAssistantData]):
    """Firestore client for assistant operations."""
    
    def __init__(self):
        super().__init__(COLLECTION, FirestoreAssistantData)
    
    def find(self, filters: dict) -> List[FirestoreAssistantData]:
        """Override to return proper type."""
        return super().find(filters)
