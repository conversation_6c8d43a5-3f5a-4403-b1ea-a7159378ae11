from typing import List
from src.domain.ports import AssistantRepository
from src.domain.models import Assistant
from .assistant_client import FirestoreAssistantClient
from .assistant_mappers import to_firestore_assistant_model, to_assistant_domain

class FirestoreAssistantRepository(AssistantRepository):
    def __init__(self):
        self.client = FirestoreAssistantClient()

    def save_assistant(self, assistant_data: Assistant) -> Assistant:
        payload = to_firestore_assistant_model(assistant_data)
        assistant_id = self.client.add(payload)
        assistant_data.id = assistant_id
        return assistant_data

    def get_assistant(self, assistant_id: str) -> Assistant:
        firestore_assistant = self.client.get(assistant_id)
        return to_assistant_domain(firestore_assistant)

    def find_assistants(self, filters: dict) -> List[Assistant]:
        firestore_assistants = self.client.find(filters)
        return [to_assistant_domain(assistant) for assistant in firestore_assistants]

    def update_assistant(self, assistant_id: str, assistant_data: Assistant) -> None:
        payload = to_firestore_assistant_model(assistant_data, partial=True)
        self.client.update(assistant_id, payload)

    def delete_assistant(self, assistant_id: str) -> None:
        self.client.delete(assistant_id)
