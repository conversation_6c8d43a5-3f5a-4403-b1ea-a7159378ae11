from src.domain.models import Assistant, BusinessConfig
from .assistant_models import FirestoreAssistantData

def to_firestore_assistant_model(assistant: Assistant, partial: bool = False) -> FirestoreAssistantData:
    if partial:
        data = {}
        mapping = {
            "user_id": "userId",
            "llm_id": "llmId",
            "agent_id": "agentId",
            "type": "type",
        }

        for domain_key, firestore_key in mapping.items():
            value = getattr(assistant, domain_key, None)
            if value is not None:
                data[firestore_key] = value

        business = assistant.business_config
        if business:
            data.update(_business_config_to_dict(business))

        return FirestoreAssistantData(**data)

    business = assistant.business_config
    business_kwargs = _business_config_to_dict(business) if business else {}

    return FirestoreAssistantData(
        userId=assistant.user_id,
        llmId=assistant.llm_id,
        agentId=assistant.agent_id,
        type=assistant.type,
        **{field: business_kwargs.get(field, None) for field in [
            "assistantName",
            "companyName",
            "timeZone",
            "noticeHours",
            "phonePrefix",
            "timeSlotOptions",
            "timeSlotInterval",
            "supportEmail",
            "supportNumber",
        ]}
    )

def to_assistant_domain(data: FirestoreAssistantData) -> Assistant:
    return Assistant(
        id=data.id,
        user_id=data.userId,
        type=data.type,
        llm_id=data.llmId,
        agent_id=data.agentId,
        business_config=BusinessConfig(
            assistant_name=data.assistantName,
            company_name=data.companyName,
            time_zone=data.timeZone,
            notice_hours=data.noticeHours,
            phone_prefix=data.phonePrefix,
            time_slot_options=data.timeSlotOptions,
            time_slot_interval=data.timeSlotInterval,
            support_email=data.supportEmail,
            support_number=data.supportNumber,
        )
    )

def _business_config_to_dict(business: BusinessConfig) -> dict:
    mapping = {
        "assistant_name": "assistantName",
        "company_name": "companyName",
        "time_zone": "timeZone",
        "notice_hours": "noticeHours",
        "phone_prefix": "phonePrefix",
        "time_slot_options": "timeSlotOptions",
        "time_slot_interval": "timeSlotInterval",
        "support_email": "supportEmail",
        "support_number": "supportNumber",
    }
    return {
        firestore_key: getattr(business, domain_key)
        for domain_key, firestore_key in mapping.items()
        if getattr(business, domain_key, None) is not None
    }
