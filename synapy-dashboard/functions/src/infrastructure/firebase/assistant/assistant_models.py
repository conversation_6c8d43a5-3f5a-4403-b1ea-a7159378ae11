from typing import Optional
from src.domain.models import SerializableModel

class FirestoreAssistantData(SerializableModel):
    id: Optional[str] = None
    userId: Optional[str] = None
    type: Optional[str] = None
    llmId: Optional[str] = None
    agentId: Optional[str] = None
    assistantName: Optional[str] = None
    companyName: Optional[str] = None
    timeZone: Optional[str] = None
    noticeHours: Optional[int] = None
    phonePrefix: Optional[str] = None
    timeSlotOptions: Optional[int] = None
    timeSlotInterval: Optional[int] = None
    supportEmail: Optional[str] = None
    supportNumber: Optional[str] = None
