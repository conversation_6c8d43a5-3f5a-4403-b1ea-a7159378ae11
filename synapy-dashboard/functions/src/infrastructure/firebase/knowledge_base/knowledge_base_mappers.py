from src.domain.models import KnowledgeBase, KnowledgeBaseSource
from .knowledge_base_models import FirestoreKnowledgeBaseData, FirestoreKnowledgeBaseSourceData

def to_firestore_knowledge_base_model(knowledge_base: KnowledgeBase, partial: bool = False) -> FirestoreKnowledgeBaseData:
    if partial:
        data = {}
        mapping = {
            "user_id": "userId",
            "knowledge_base_id": "knowledgeBaseId",
            "name": "name",
            "type": "type",
            "custom_trigger": "customTrigger",
            "status": "status",
            "last_refreshed_timestamp": "lastRefreshedTimestamp",
        }

        for domain_key, firestore_key in mapping.items():
            value = getattr(knowledge_base, domain_key, None)
            if value is not None:
                data[firestore_key] = value

        if knowledge_base.sources:
            data["sources"] = [_knowledge_base_source_to_dict(source) for source in knowledge_base.sources]

        return FirestoreKnowledgeBaseData(**data)

    sources = None
    if knowledge_base.sources:
        sources = [_knowledge_base_source_to_dict(source) for source in knowledge_base.sources]

    return FirestoreKnowledgeBaseData(
        userId=knowledge_base.user_id,
        knowledgeBaseId=knowledge_base.knowledge_base_id,
        name=knowledge_base.name,
        type=knowledge_base.type,
        customTrigger=knowledge_base.custom_trigger,
        status=knowledge_base.status,
        sources=sources,
        lastRefreshedTimestamp=knowledge_base.last_refreshed_timestamp,
    )

def to_knowledge_base_domain(data: FirestoreKnowledgeBaseData) -> KnowledgeBase:
    sources = None
    if data.sources:
        sources = [_dict_to_knowledge_base_source(source) for source in data.sources]

    return KnowledgeBase(
        id=data.id,
        user_id=data.userId,
        knowledge_base_id=data.knowledgeBaseId,
        name=data.name,
        type=data.type,
        custom_trigger=data.customTrigger,
        status=data.status,
        sources=sources,
        last_refreshed_timestamp=data.lastRefreshedTimestamp,
    )

def _knowledge_base_source_to_dict(source: KnowledgeBaseSource) -> FirestoreKnowledgeBaseSourceData:
    return FirestoreKnowledgeBaseSourceData(
        type=source.type,
        sourceId=source.source_id,
        name=source.name,
        url=source.url,
        text=source.text,
    )

def _dict_to_knowledge_base_source(data: FirestoreKnowledgeBaseSourceData) -> KnowledgeBaseSource:
    return KnowledgeBaseSource(
        type=data.type,
        source_id=data.sourceId,
        name=data.name,
        url=data.url,
        text=data.text,
    )
