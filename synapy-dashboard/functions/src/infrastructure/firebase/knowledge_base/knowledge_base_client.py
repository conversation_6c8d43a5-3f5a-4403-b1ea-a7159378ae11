from typing import List
from ..shared import BaseFirestoreClient
from .knowledge_base_models import FirestoreKnowledgeBaseData

COLLECTION = 'knowledge_bases'

class FirestoreKnowledgeBaseClient(BaseFirestoreClient[FirestoreKnowledgeBaseData]):
    """Firestore client for knowledge base operations."""
    
    def __init__(self):
        super().__init__(COLLECTION, FirestoreKnowledgeBaseData)
    
    def find(self, filters: dict) -> List[FirestoreKnowledgeBaseData]:
        """Override to return proper type."""
        return super().find(filters)
