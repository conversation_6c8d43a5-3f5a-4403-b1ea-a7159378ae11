from typing import Optional, List
from src.domain.models import SerializableModel

class FirestoreKnowledgeBaseSourceData(SerializableModel):
    type: Optional[str] = None
    sourceId: Optional[str] = None
    name: Optional[str] = None
    url: Optional[str] = None
    text: Optional[str] = None

class FirestoreKnowledgeBaseData(SerializableModel):
    id: Optional[str] = None
    userId: Optional[str] = None
    knowledgeBaseId: Optional[str] = None  # External Retell ID
    name: Optional[str] = None
    type: Optional[str] = None
    customTrigger: Optional[str] = None
    status: Optional[str] = None
    sources: Optional[List[FirestoreKnowledgeBaseSourceData]] = None
    lastRefreshedTimestamp: Optional[int] = None
