import io
import requests
import urllib.parse
from typing import List
from firebase_admin import storage
from src.domain.ports import FileRepository
from src.shared.logging import Logger
from .shared.exceptions import StorageError, StorageReadError

class FirebaseStorageRepository(FileRepository):
  """Unified repository for handling file operations with Firebase Storage."""

  def __init__(self):
    self.bucket = storage.bucket()

  def get_file_text(self, file_path: str) -> str:
    """Download a text file from Google Cloud Storage."""
    try:
      blob = self.bucket.blob(file_path)
      return blob.download_as_text()
    except Exception as e:
      raise StorageReadError(file_path, e)

  def download_file_from_url(self, file_url: str) -> io.IOBase:
    """
    Download a file from a Cloud Storage URL and return as a binary stream.
    Uses Firebase Admin SDK instead of requests library.

    Args:
      file_url: The Cloud Storage URL

    Returns:
      Binary file stream ready for reading
    """
    try:
      if self._is_download_url(file_url):
        # Download directly from public/private URL (like Firebase Emulator or signed URL)
        response = requests.get(file_url)
        response.raise_for_status()
        file_stream = io.BytesIO(response.content)
        file_stream.seek(0)
        return file_stream
      else:
        raise StorageError(f"The URL is not a valid Firebase Storage URL: {file_url}")

    except Exception as e:
      raise StorageReadError(file_url, e)

  def delete_files(self, file_paths: List[str]) -> None:
    """
    Delete files from Firebase Storage.

    Args:
      file_paths: List of storage paths to delete
    """
    for file_path in file_paths:
      try:
        blob = self.bucket.blob(file_path)
        blob.delete()
      except Exception as e:
        raise StorageError(f"Failed to delete file {file_path}", e)

  def delete_files_from_url(self, file_urls: List[str]) -> None:
    file_paths_to_delete = []

    for url in file_urls:
      file_path = self._extract_file_path_from_url(url)
      if file_path:
        file_paths_to_delete.append(file_path)

    Logger.info(f"🗑️ Storage Repository: {len(file_paths_to_delete)} files marked for cleanup")

    if file_paths_to_delete:
      try:
        self.delete_files(file_paths_to_delete)
      except Exception as e:
        Logger.warn(f"Storage Repository: Failed to delete files: {str(e)}")

  def get_file_download_url(self, file_path: str) -> str:
    """
    Get a download URL for a file.

    Args:
      file_path: Storage path of the file

    Returns:
      Download URL
    """
    try:
      blob = self.bucket.blob(file_path)
      return blob.public_url
    except Exception as e:
      raise StorageReadError(file_path, e)

  def _is_download_url(self, url: str) -> bool:
    """
    Check if the URL is a direct Firebase Storage download URL.
    """
    return any(domain in url for domain in [
      "firebasestorage.googleapis.com",
      "firebasestorage.app",
      "storage.googleapis.com",
      "storage.cloud.google.com",
    ])

  def _extract_file_path_from_url(self, file_url: str) -> str:
    """
    Extract the blob path from a Firebase or GCS public URL.

    Args:
      file_url: Public Cloud Storage URL

    Returns:
      Path to the file within the bucket (e.g. "folder/file.pdf")
    """
    try:
      # Handle different URL formats:
      # https://storage.googleapis.com/bucket-name/path/to/file
      # https://firebasestorage.googleapis.com/v0/b/bucket-name/o/path%2Fto%2Ffile?alt=media&token=...
      parsed_url = urllib.parse.urlparse(file_url)
      netloc = parsed_url.netloc
      path = parsed_url.path

      if (
        'firebasestorage.googleapis.com' in netloc or
        'firebasestorage.app' in netloc or
        'localhost' in netloc
      ):
        # Format: /v0/b/{bucket}/o/{path}?alt=media&token=...
        # We want the part after "/o/"
        path_parts = path.split('/o/')
        if len(path_parts) > 1:
          encoded_path = path_parts[1].split('?')[0]  # remove query
          return urllib.parse.unquote(encoded_path)
      elif 'storage.googleapis.com' in netloc or 'storage.cloud.google.com' in netloc:
        # Format: /{bucket}/{path/to/file}
        path_parts = path.strip('/').split('/')
        if len(path_parts) > 1:
          return '/'.join(path_parts[1:])  # skip bucket name

      Logger.warn(f"Storage Repository: Unknown storage URL format: {file_url}")
      return ""

    except Exception as e:
      Logger.warn(f"Storage Repository: Failed to extract file path from URL {file_url}: {str(e)}")
      return ""
