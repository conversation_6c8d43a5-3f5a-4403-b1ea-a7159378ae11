from src.domain.models import Assistant, AgentConfig, LLMConfig
from .assistant_models import (
    CreateAgentRequest,
    AgentResponse,
    CreateLLMRequest,
    LLMResponse,
    ResponseEngine,
)

DEFAULT_RESPONSE_ENGINE_TYPE = "retell-llm"

def to_retell_agent_request(input: Assistant) -> CreateAgentRequest:
    """Convert domain model to Retell API agent request model."""
    return CreateAgentRequest(
        response_engine=ResponseEngine(
            type=DEFAULT_RESPONSE_ENGINE_TYPE,
            llm_id=input.llm_id or "",
        ),
        voice_id=getattr(input.agent_config, "voice_id", "11labs-Adrian"),
        agent_name=getattr(input.agent_config, "agent_name", None),
        voice_model=getattr(input.agent_config, "voice_model", None),
        voice_temperature=getattr(input.agent_config, "voice_temperature", None),
        voice_speed=getattr(input.agent_config, "voice_speed", None),
        responsiveness=getattr(input.agent_config, "responsiveness", None),
        backchannel_words=getattr(input.agent_config, "backchannel_words", None),
        ambient_sound=getattr(input.agent_config, "ambient_sound", None),
        ambient_sound_volume=getattr(input.agent_config, "ambient_sound_volume", None),
        language=getattr(input.agent_config, "language", None),
        normalize_for_speech=True,
    )

def to_retell_llm_request(input: Assistant) -> CreateLLMRequest:
    """Convert domain model to Retell API LLM request model."""
    return CreateLLMRequest(
        model=getattr(input.llm_config, "model", None),
        model_temperature=getattr(input.llm_config, "model_temperature", None),
        general_prompt=getattr(input.llm_config, "general_prompt", None),
        begin_message=getattr(input.llm_config, "begin_message", None),
        knowledge_base_ids=getattr(input.llm_config, "knowledge_base_ids", None),
    )

def to_assistant_agent_config(input: AgentResponse) -> AgentConfig:
    """Convert Retell API agent response to domain agent config."""
    return AgentConfig(
        voice_temperature=input.voice_temperature,
        voice_speed=input.voice_speed,
        responsiveness=input.responsiveness,
        backchannel_words=input.backchannel_words,
        ambient_sound=input.ambient_sound,
        language=input.language,
        voice_id=input.voice_id,
    )

def to_assistant_llm_config(input: LLMResponse) -> LLMConfig:
    """Convert Retell API LLM response to domain LLM config."""
    return LLMConfig(
        llm_id=input.llm_id,
        model_temperature=input.model_temperature,
        begin_message=input.begin_message,
        knowledge_base_ids=input.knowledge_base_ids,
        general_prompt=input.general_prompt,
    )
