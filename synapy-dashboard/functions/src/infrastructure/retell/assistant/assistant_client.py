from ..shared import RetellClient
from .assistant_models import CreateAgentRequest, AgentResponse, CreateLLMRequest, LLMResponse

class RetellAssistantClient(RetellClient):
    """Retell API client for assistant operations."""

    # AGENT
    def create_retell_agent(self, agent_data: CreateAgentRequest) -> AgentResponse:
        try:
            payload = agent_data.to_dict()
            api_response = self.client.agent.create(**payload)
            return AgentResponse(**api_response.dict())
        except Exception as e:
            self._handle_exception(e)

    def update_retell_agent(self, agent_id: str, agent_data: CreateAgentRequest) -> AgentResponse:
        try:
            payload = agent_data.to_dict()
            api_response = self.client.agent.update(agent_id, **payload)
            return AgentResponse(**api_response.dict())
        except Exception as e:
            self._handle_exception(e)

    def get_retell_agent(self, agent_id: str) -> AgentResponse:
        try:
            api_response = self.client.agent.retrieve(agent_id)
            return AgentResponse(**api_response.dict())
        except Exception as e:
            self._handle_exception(e)

    def delete_retell_agent(self, agent_id: str) -> None:
        try:
            self.client.agent.delete(agent_id)
        except Exception as e:
            self._handle_exception(e)

    # LLM
    def create_retell_llm(self, llm_data: CreateLLMRequest) -> LLMResponse:
        try:
            payload = llm_data.to_dict()
            api_response = self.client.llm.create(**payload)
            return LLMResponse(**api_response.dict())
        except Exception as e:
            self._handle_exception(e)

    def update_retell_llm(self, llm_id: str, llm_data: CreateLLMRequest) -> LLMResponse:
        try:
            payload = llm_data.to_dict()
            api_response = self.client.llm.update(llm_id, **payload)
            return LLMResponse(**api_response.dict())
        except Exception as e:
            self._handle_exception(e)

    def get_retell_llm(self, llm_id: str) -> LLMResponse:
        try:
            api_response = self.client.llm.retrieve(llm_id)
            return LLMResponse(**api_response.dict())
        except Exception as e:
            self._handle_exception(e)

    def delete_retell_llm(self, llm_id: str) -> None:
        try:
            self.client.llm.delete(llm_id)
        except Exception as e:
            self._handle_exception(e)
