from typing import List, Optional, Dict
from src.domain.models import SerializableModel

# Shared models for both request and response
class ResponseEngine(SerializableModel):
    """Response engine configuration."""
    type: str  # "retell-llm"
    llm_id: str
    version: Optional[int] = None

class PronunciationEntry(SerializableModel):
    """Pronunciation dictionary entry."""
    word: str
    alphabet: str  # "ipa"
    phoneme: str

class PostCallAnalysisData(SerializableModel):
    """Post-call analysis data configuration."""
    type: str  # "string", "number", "boolean", etc.
    name: str
    description: str
    examples: Optional[List[str]] = None

class UserDTMFOptions(SerializableModel):
    """User DTMF options configuration."""
    digit_limit: Optional[int] = None
    termination_key: Optional[str] = None
    timeout_ms: Optional[int] = None

class LLMTool(SerializableModel):
    """LLM tool configuration."""
    type: str
    name: str
    description: str
    # Additional fields depend on tool type

class LLMState(SerializableModel):
    """LLM state configuration."""
    name: str
    state_prompt: str
    edges: Optional[List[Dict[str, str]]] = None
    tools: Optional[List[LLMTool]] = None

# AGENT REQUEST MODELS (for API input)
class CreateAgentRequest(SerializableModel):
    """Request model for creating an agent."""
    response_engine: ResponseEngine
    voice_id: str
    agent_name: Optional[str] = None
    voice_model: Optional[str] = None
    fallback_voice_ids: Optional[List[str]] = None
    voice_temperature: Optional[float] = None
    voice_speed: Optional[float] = None
    volume: Optional[float] = None
    responsiveness: Optional[float] = None
    interruption_sensitivity: Optional[float] = None
    enable_backchannel: Optional[bool] = None
    backchannel_frequency: Optional[float] = None
    backchannel_words: Optional[List[str]] = None
    reminder_trigger_ms: Optional[int] = None
    reminder_max_count: Optional[int] = None
    ambient_sound: Optional[str] = None
    ambient_sound_volume: Optional[float] = None
    language: Optional[str] = None
    webhook_url: Optional[str] = None
    boosted_keywords: Optional[List[str]] = None
    enable_transcription_formatting: Optional[bool] = None
    opt_out_sensitive_data_storage: Optional[bool] = None
    opt_in_signed_url: Optional[bool] = None
    pronunciation_dictionary: Optional[List[PronunciationEntry]] = None
    normalize_for_speech: Optional[bool] = None
    end_call_after_silence_ms: Optional[int] = None
    max_call_duration_ms: Optional[int] = None
    enable_voicemail_detection: Optional[bool] = None
    voicemail_message: Optional[str] = None
    voicemail_detection_timeout_ms: Optional[int] = None
    post_call_analysis_data: Optional[List[PostCallAnalysisData]] = None
    post_call_analysis_model: Optional[str] = None
    begin_message_delay_ms: Optional[int] = None
    ring_duration_ms: Optional[int] = None
    stt_mode: Optional[str] = None
    vocab_specialization: Optional[str] = None
    allow_user_dtmf: Optional[bool] = None
    user_dtmf_options: Optional[UserDTMFOptions] = None
    denoising_mode: Optional[str] = None

# AGENT RESPONSE MODELS (for API output)
class AgentResponse(SerializableModel):
    """Response model for agent operations."""
    agent_id: str
    version: int
    is_published: bool
    response_engine: ResponseEngine
    agent_name: Optional[str] = None
    voice_id: str
    voice_model: Optional[str] = None
    fallback_voice_ids: Optional[List[str]] = None
    voice_temperature: Optional[float] = None
    voice_speed: Optional[float] = None
    volume: Optional[float] = None
    responsiveness: Optional[float] = None
    interruption_sensitivity: Optional[float] = None
    enable_backchannel: Optional[bool] = None
    backchannel_frequency: Optional[float] = None
    backchannel_words: Optional[List[str]] = None
    reminder_trigger_ms: Optional[int] = None
    reminder_max_count: Optional[int] = None
    ambient_sound: Optional[str] = None
    ambient_sound_volume: Optional[float] = None
    language: Optional[str] = None
    webhook_url: Optional[str] = None
    boosted_keywords: Optional[List[str]] = None
    enable_transcription_formatting: Optional[bool] = None
    opt_out_sensitive_data_storage: Optional[bool] = None
    opt_in_signed_url: Optional[bool] = None
    pronunciation_dictionary: Optional[List[PronunciationEntry]] = None
    normalize_for_speech: Optional[bool] = None
    end_call_after_silence_ms: Optional[int] = None
    max_call_duration_ms: Optional[int] = None
    enable_voicemail_detection: Optional[bool] = None
    voicemail_message: Optional[str] = None
    voicemail_detection_timeout_ms: Optional[int] = None
    post_call_analysis_data: Optional[List[PostCallAnalysisData]] = None
    post_call_analysis_model: Optional[str] = None
    begin_message_delay_ms: Optional[int] = None
    ring_duration_ms: Optional[int] = None
    stt_mode: Optional[str] = None
    vocab_specialization: Optional[str] = None
    allow_user_dtmf: Optional[bool] = None
    user_dtmf_options: Optional[UserDTMFOptions] = None
    denoising_mode: Optional[str] = None
    last_modification_timestamp: int

    def get_llm_id(self) -> Optional[str]:
        if self.response_engine:
            return self.response_engine.llm_id
        return None

# LLM REQUEST MODELS (for API input)
class CreateLLMRequest(SerializableModel):
    """Request model for creating an LLM."""
    model: Optional[str] = None  # "gpt-4o", "gpt-4o-mini", etc.
    s2s_model: Optional[str] = None  # "gpt-4o-realtime"
    model_temperature: Optional[float] = None
    model_high_priority: Optional[bool] = None
    tool_call_strict_mode: Optional[bool] = None
    general_prompt: Optional[str] = None
    general_tools: Optional[List[LLMTool]] = None
    states: Optional[List[LLMState]] = None
    starting_state: Optional[str] = None
    begin_message: Optional[str] = None
    default_dynamic_variables: Optional[Dict[str, str]] = None
    knowledge_base_ids: Optional[List[str]] = None
    inbound_dynamic_variables_webhook_url: Optional[str] = None

# LLM RESPONSE MODELS (for API output)
class LLMResponse(SerializableModel):
    """Response model for LLM operations."""
    llm_id: str
    version: int
    is_published: bool
    model: Optional[str] = None
    s2s_model: Optional[str] = None
    model_temperature: Optional[float] = None
    model_high_priority: Optional[bool] = None
    tool_call_strict_mode: Optional[bool] = None
    general_prompt: Optional[str] = None
    general_tools: Optional[List[LLMTool]] = None
    states: Optional[List[LLMState]] = None
    starting_state: Optional[str] = None
    begin_message: Optional[str] = None
    default_dynamic_variables: Optional[Dict[str, str]] = None
    knowledge_base_ids: Optional[List[str]] = None
    inbound_dynamic_variables_webhook_url: Optional[str] = None
    last_modification_timestamp: int
