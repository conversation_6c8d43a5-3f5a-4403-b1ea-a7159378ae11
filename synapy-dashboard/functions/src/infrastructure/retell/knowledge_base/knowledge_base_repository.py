from typing import List

from src.domain.ports import KnowledgeBaseRepository
from src.domain.models import KnowledgeBase, KnowledgeBaseSource
from src.infrastructure.firebase import FirebaseStorageRepository
from .knowledge_base_client import RetellKnowledgeBaseClient
from .knowledge_base_mappers import to_retell_knowledge_base_request, to_retell_add_sources_request, to_domain_knowledge_base
from ..shared.exceptions import RetellAPIError

class RetellKnowledgeBaseRepository(KnowledgeBaseRepository):

  def __init__(self):
    self.client = RetellKnowledgeBaseClient()
    self.storage = FirebaseStorageRepository()

  def _cleanup_temporary_files(self, sources: List[KnowledgeBaseSource]) -> None:
    """
    Clean up temporary files from Firebase Cloud Storage after successful Retell AI processing.

    Args:
      sources: List of knowledge base sources that may contain document URLs
    """
    # Extract file urls from document sources
    file_urls = []

    for source in sources:
      if source.type == "document" and source.url:
        file_urls.append(source.url)

    self.storage.delete_files_from_url(file_urls)

  def save_knowledge_base(self, knowledge_base: KnowledgeBase) -> KnowledgeBase:
    """Create a new knowledge base in Retell AI."""
    try:
      retell_kb_request = to_retell_knowledge_base_request(knowledge_base, self.storage)
      retell_kb_response = self.client.create_retell_knowledge_base(retell_kb_request)

      # Convert response to domain model
      result_kb = to_domain_knowledge_base(retell_kb_response)

      merged_data = {
        **knowledge_base.dict(),
        **result_kb.dict(exclude_unset=True, exclude_none=True)
      }

      return KnowledgeBase(**merged_data)
    except Exception as e:
      raise RetellAPIError(f"Failed to create knowledge base in Retell: {e}")

  def get_knowledge_base(self, knowledge_base_id: str) -> KnowledgeBase:
    """Retrieve a knowledge base by its external ID from Retell AI."""
    try:
      retell_kb = self.client.get_retell_knowledge_base(knowledge_base_id)
      return to_domain_knowledge_base(retell_kb)
    except Exception as e:
      raise RetellAPIError(f"Failed to get knowledge base from Retell: {e}")

  def find_knowledge_bases(self, _filters: dict) -> List[KnowledgeBase]:
    """List all knowledge bases from Retell AI. Note: Retell doesn't support filtering."""
    # Note: Retell API doesn't support filtering, so filters parameter is ignored
    del _filters  # Suppress unused parameter warning
    try:
      retell_kbs = self.client.list_retell_knowledge_bases()
      return [to_domain_knowledge_base(kb) for kb in retell_kbs]
    except Exception as e:
      raise RetellAPIError(f"Failed to list knowledge bases from Retell: {e}")

  def add_knowledge_base_sources(self, knowledge_base_id: str, sources: List[KnowledgeBaseSource]) -> KnowledgeBase:
    """Add sources to an existing knowledge base in Retell AI."""
    try:
      retell_add_request = to_retell_add_sources_request(sources, self.storage)
      retell_kb_response = self.client.add_retell_knowledge_base_sources(knowledge_base_id, retell_add_request)

      # Convert response to domain model
      result_kb = to_domain_knowledge_base(retell_kb_response)

      return result_kb
    except Exception as e:
      raise RetellAPIError(f"Failed to add sources to knowledge base in Retell: {e}")

  def delete_knowledge_base_source(self, knowledge_base_id: str, source_id: str) -> KnowledgeBase:
    """Delete a source from a knowledge base in Retell AI."""
    try:
      retell_kb_response = self.client.delete_retell_knowledge_base_source(knowledge_base_id, source_id)
      return to_domain_knowledge_base(retell_kb_response)
    except Exception as e:
      raise RetellAPIError(f"Failed to delete source from knowledge base in Retell: {e}")

  def delete_knowledge_base(self, knowledge_base_id: str) -> None:
    """Delete a knowledge base by its external ID from Retell AI."""
    try:
      self.client.delete_retell_knowledge_base(knowledge_base_id)
    except Exception as e:
      raise RetellAPIError(f"Failed to delete knowledge base from Retell: {e}")
