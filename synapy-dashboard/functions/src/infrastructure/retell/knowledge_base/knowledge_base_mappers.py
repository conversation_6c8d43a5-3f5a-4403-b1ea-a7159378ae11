import io
from typing import List, Optional
from src.domain.models import KnowledgeBase, KnowledgeBaseSource
from src.infrastructure.firebase import FirebaseStorageRepository
from src.shared.logging import Logger
from .knowledge_base_models import (
    CreateKnowledgeBaseRequest,
    AddKnowledgeBaseSourcesRequest,
    KnowledgeBaseResponse,
    KnowledgeBaseSourceResponse,
    KnowledgeBaseTextInput,
)

def _process_text_source(source: KnowledgeBaseSource) -> Optional[KnowledgeBaseTextInput]:
    """Process a text source into Retell format."""
    if source.type == "text" and hasattr(source, 'text') and hasattr(source, 'title'):
        return KnowledgeBaseTextInput(text=source.text, title=source.name)
    return None

def _process_url_source(source: KnowledgeBaseSource) -> Optional[str]:
    """Process a URL source into Retell format."""
    if source.type == "url" and source.url:
        return source.url
    return None

def _process_file_source(source: KnowledgeBaseSource, storage: Optional[FirebaseStorageRepository]) -> Optional[io.IOBase]:
    """Process a file source into Retell format."""
    if source.type == "document" and source.url and storage:
        try:
            return storage.download_file_from_url(source.url)
        except Exception as e:
            Logger.warn(f"Knowledge Base Mapper: Failed to download file from {source.url}: {e}")
    return None

def _process_sources(sources: List[KnowledgeBaseSource], storage: Optional[FirebaseStorageRepository]) -> tuple:
    """Process all sources and return texts, urls, files."""
    texts, urls, files = [], [], []

    for source in sources:
        text_input = _process_text_source(source)
        if text_input:
            texts.append(text_input)

        url = _process_url_source(source)
        if url:
            urls.append(url)

        file_stream = _process_file_source(source, storage)
        if file_stream:
            files.append(file_stream)

    return texts, urls, files

def to_retell_knowledge_base_request(input: KnowledgeBase, storage: Optional[FirebaseStorageRepository] = None) -> CreateKnowledgeBaseRequest:
    """Convert domain model to Retell API request model."""
    texts, urls, files = _process_sources(input.sources or [], storage)

    return CreateKnowledgeBaseRequest(
        knowledge_base_name=input.name or "Untitled Knowledge Base",
        knowledge_base_texts=texts if texts else None,
        knowledge_base_urls=urls if urls else None,
        knowledge_base_files=files if files else None,
        enable_auto_refresh=True,
    )

def to_retell_add_sources_request(sources: List[KnowledgeBaseSource], storage: Optional[FirebaseStorageRepository] = None) -> AddKnowledgeBaseSourcesRequest:
    """Convert domain sources to Retell API add sources request model."""
    texts, urls, files = _process_sources(sources, storage)

    return AddKnowledgeBaseSourcesRequest(
        knowledge_base_texts=texts if texts else None,
        knowledge_base_urls=urls if urls else None,
        knowledge_base_files=files if files else None,
    )

def to_retell_knowledge_base_response(input: KnowledgeBase) -> KnowledgeBaseResponse:
    """Convert domain model to Retell API response model (for testing/mocking)."""
    sources = None
    if input.sources:
        sources = [to_retell_knowledge_base_source_response(source) for source in input.sources]

    return KnowledgeBaseResponse(
        knowledge_base_id=input.knowledge_base_id or "",
        knowledge_base_name=input.name or "",
        status=input.status or "ready",
        knowledge_base_sources=sources,
        last_refreshed_timestamp=input.last_refreshed_timestamp,
    )

def to_retell_knowledge_base_source_response(input: KnowledgeBaseSource) -> KnowledgeBaseSourceResponse:
    """Convert domain source to Retell API response source model."""
    return KnowledgeBaseSourceResponse(
        type=input.type,
        source_id=input.source_id,
        filename=input.name,
        file_url=input.url,
    )

def to_domain_knowledge_base(input: KnowledgeBaseResponse) -> KnowledgeBase:
    """Convert Retell API response model to domain model."""
    sources = None
    if input.knowledge_base_sources:
        sources = [to_domain_knowledge_base_source(source) for source in input.knowledge_base_sources]

    return KnowledgeBase(
        knowledge_base_id=input.knowledge_base_id,
        name=input.knowledge_base_name,
        status=input.status,
        sources=sources,
        last_refreshed_timestamp=input.last_refreshed_timestamp,
    )

def to_domain_knowledge_base_source(input: KnowledgeBaseSourceResponse) -> KnowledgeBaseSource:
    """Convert Retell API response source model to domain source model."""
    return KnowledgeBaseSource(
        type=input.type,
        source_id=input.source_id,
        name=input.filename,
        url=input.file_url,
    )


