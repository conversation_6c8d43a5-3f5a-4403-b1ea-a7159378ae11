from typing import List
from src.shared.logging import Logger
from ..shared import RetellClient
from .knowledge_base_models import CreateKnowledgeBaseRequest, AddKnowledgeBaseSourcesRequest, KnowledgeBaseResponse

class RetellKnowledgeBaseClient(RetellClient):
    """Retell API client for knowledge base operations."""

    def create_retell_knowledge_base(self, kb_data: CreateKnowledgeBaseRequest) -> KnowledgeBaseResponse:
        try:
            payload = kb_data.to_dict()
            files = kb_data.knowledge_base_files
            api_response = self.client.knowledge_base.create(**payload, knowledge_base_files=files)
            return KnowledgeBaseResponse(**api_response.dict())
        except Exception as e:
            self._handle_exception(e)

    def add_retell_knowledge_base_sources(self, kb_id: str, sources_data: AddKnowledgeBaseSourcesRequest) -> KnowledgeBaseResponse:
        try:
            payload = sources_data.to_dict()
            files = sources_data.knowledge_base_files
            api_response = self.client.knowledge_base.add_sources(kb_id, **payload, knowledge_base_files=files)
            return KnowledgeBaseResponse(**api_response.dict())
        except Exception as e:
            self._handle_exception(e)

    def delete_retell_knowledge_base_source(self, kb_id: str, source_id: str) -> KnowledgeBaseResponse:
        try:
            api_response = self.client.knowledge_base.delete_source(kb_id, source_id)
            return KnowledgeBaseResponse(**api_response.dict())
        except Exception as e:
            self._handle_exception(e)

    def get_retell_knowledge_base(self, kb_id: str) -> KnowledgeBaseResponse:
        try:
            api_response = self.client.knowledge_base.retrieve(kb_id)
            return KnowledgeBaseResponse(**api_response.dict())
        except Exception as e:
            self._handle_exception(e)

    def list_retell_knowledge_bases(self) -> List[KnowledgeBaseResponse]:
        try:
            api_response = self.client.knowledge_base.list()
            return [KnowledgeBaseResponse(**kb.dict()) for kb in api_response]
        except Exception as e:
            self._handle_exception(e)

    def delete_retell_knowledge_base(self, kb_id: str) -> None:
        try:
            self.client.knowledge_base.delete(kb_id)
        except Exception as e:
            self._handle_exception(e)
