from functools import wraps
from typing import Callable, Any
import logging

def log_request(func: Callable) -> Callable:
    """
    Middleware to log request details.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        logging.info(f"📥 Processing request for {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logging.info(f"✅ Successfully processed {func.__name__}")
            return result
        except Exception as e:
            logging.error(f"❌ Error in {func.__name__}: {str(e)}")
            raise

    return wrapper
