from functools import wraps
from typing import Callable, Any
from firebase_functions.https_fn import on_call, CallableRequest, AuthData, HttpsError
from src.domain.exceptions import AppError
from src.shared.logging import log_callable_calls
from .core import VALID_CODES

def firebase_function(func: Callable[[dict], dict]) -> Callable:
    """
    Decorator that wraps a handler function to be a Firebase Function.
    Handles common concerns like authentication, logging, and error handling.
    """
    @on_call()
    @log_callable_calls
    @wraps(func)
    def wrapper(request: CallableRequest) -> dict:
        try:
            return func(request.data)
        except AppError as e:
            code = e.code if e.code in VALID_CODES else "internal"
            raise HttpsError(code or "internal", f"Error in {func.__name__}: {str(e)}")
        except Exception as e:
            raise HttpsError("internal", f"Internal error in {func.__name__}: {str(e)}")

    return wrapper

def require_auth(func: Callable[[CallableRequest], dict]) -> Callable:
    """
    Decorator that ensures the request is authenticated.
    """
    @wraps(func)
    def wrapper(request: CallableRequest) -> dict:
        if not request.auth:
            raise HttpsError("unauthenticated", "Authentication required.")
        return func(request)

    return wrapper

def authenticated_firebase_function(func: Callable[[dict], dict]) -> Callable:
    """
    Decorator that combines firebase_function and require_auth.
    """
    @on_call()
    @log_callable_calls
    @wraps(func)
    def wrapper(request: CallableRequest) -> dict:
        # Check authentication
        if not request.auth:
            raise HttpsError("unauthenticated", "Authentication required.")

        try:
            return func(request.data)
        except AppError as e:
            raise HttpsError(e.code or "internal", f"Error in {func.__name__}: {str(e)}")
        except Exception as e:
            raise HttpsError("internal", f"Internal error in {func.__name__}: {str(e)}")

    return wrapper
