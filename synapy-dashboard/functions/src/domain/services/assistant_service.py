from typing import List
from src.domain.models import Assistant, BusinessConfig, LLMConfig, AgentConfig
from src.infrastructure import FirestoreAssistantRepository, FirebaseStorageRepository, RetellAssistantRepository
from src.infrastructure.firebase import FirestoreError
from src.infrastructure.retell import RetellAPIError
from src.shared.util import has_values

# IMPORTANT: assistantName refers to the name by which the assistant will be identified within the call with a client.
DEFAULT_PROMPT_VALUES = {
    'assistantName': 'Synapy',
    'timeZone': 'Europe/Madrid',
    'noticeHours': 3,
    'phonePrefix': '+34',
    'timeSlotOptions': 3,
    'timeSlotInterval': 30,
    'supportEmail': '',
    'supportNumber': '',
}

PROMPT_FILES = {
    'reservations': 'prompts/v1/prompt_llm_reservations.md',
    'prospection': 'prompts/v1/prompt_llm_leads.md',
    'advisor': 'prompts/v1/prompt_llm_advisor.md'
}

DEFAULT_LLM_MODEL         = "gpt-4o-mini"
DEFAULT_AGENT_VOICE_ID    = "custom-Carolina"
DEFAULT_AGENT_VOICE_MODEL = "eleven_turbo_v2_5"
DEFAULT_AGENT_LANG        = "es-ES"

class AssistantService:
    def __init__(self):
        self.firestore  = FirestoreAssistantRepository()
        self.storage    = FirebaseStorageRepository()
        self.retell     = RetellAssistantRepository()

    def create(self, assistant: Assistant) -> Assistant:
        """Create a new assistant."""
        assistant.business_config = _map_business_config(assistant)
        assistant.agent_config    = _map_agent_config(assistant)
        assistant.llm_config      = self._map_llm_config(assistant)

        try:
            assistant = self.retell.save_assistant(assistant)
            assistant = self.firestore.save_assistant(assistant)

            return assistant
        except FirestoreError as e:
            self.retell.delete_assistant(assistant.agent_id)
            raise e

    def update(self, assistant_id: str, assistant: Assistant) -> None:
        """Update an existing assistant."""
        should_update_firestore = has_values(assistant.business_config)

        original_assistant = self.firestore.get_assistant(assistant_id)

        if should_update_firestore:
            self.firestore.update_assistant(assistant_id, assistant)

        assistant.llm_id = original_assistant.llm_id

        if has_values(assistant.llm_config) or has_values(assistant.agent_config):
            try:
                self.retell.update_assistant(
                    assistant_id = original_assistant.agent_id,
                    assistant = assistant,
                )
            except RetellAPIError as e:
                if should_update_firestore:
                    self.firestore.update_assistant(assistant_id, original_assistant)
                raise e

    def get(self, assistant_id: str) -> Assistant:
        """Retrieve an assistant by its ID."""
        assistant = self.firestore.get_assistant(assistant_id)
        retell_assistant = self.retell.get_assistant(assistant.agent_id)

        assistant.llm_config   = retell_assistant.llm_config
        assistant.agent_config = retell_assistant.agent_config

        return assistant

    def fetch_by_user(self, user_id: str) -> List[Assistant]:
        """Fetch all assistants associated with a user."""
        filters = {"userId": user_id}
        return self.firestore.find_assistants(filters)

    def delete(self, assistant_id: str) -> None:
        """Delete an assistant by its ID."""
        assistant = self.firestore.get_assistant(assistant_id)
        self.retell.delete_assistant(assistant.agent_id)
        self.firestore.delete_assistant(assistant_id)

    def _map_llm_config(self, assistant: Assistant) -> LLMConfig:
        """Map the LLM configuration to the Assistant model."""
        llm_config = assistant.llm_config or LLMConfig()
        llm_config.model          = DEFAULT_LLM_MODEL
        llm_config.general_prompt = self._get_prompt_content(assistant.type)
        return llm_config

    def _get_prompt_content(self, prompt_type: str) -> str:
        """Get the content of the specified prompt file."""
        file_path = _get_prompt_file_path(prompt_type)
        return self.storage.get_file_text(file_path)

def _get_prompt_file_path(prompt_type: str) -> str:
    """Get the file path for the specified prompt type."""
    if prompt_type not in PROMPT_FILES:
        raise ValueError(f"Invalid prompt type: {prompt_type}")
    return PROMPT_FILES[prompt_type]

def _map_business_config(assistant: Assistant) -> BusinessConfig:
    """Map the business configuration to the Assistant model."""
    business_config = assistant.business_config or BusinessConfig()
    business_config.assistant_name      = DEFAULT_PROMPT_VALUES['assistantName']
    business_config.time_zone           = DEFAULT_PROMPT_VALUES['timeZone']
    business_config.notice_hours        = DEFAULT_PROMPT_VALUES['noticeHours']
    business_config.phone_prefix        = DEFAULT_PROMPT_VALUES['phonePrefix']
    business_config.time_slot_options   = DEFAULT_PROMPT_VALUES['timeSlotOptions']
    business_config.time_slot_interval  = DEFAULT_PROMPT_VALUES['timeSlotInterval']
    business_config.support_email       = DEFAULT_PROMPT_VALUES['supportEmail']
    business_config.support_number      = DEFAULT_PROMPT_VALUES['supportNumber']
    return business_config

def _map_agent_config(assistant: Assistant) -> AgentConfig:
    """Map the agent configuration to the Assistant model."""
    agent_config = assistant.agent_config or AgentConfig()
    agent_config.voice_id          = DEFAULT_AGENT_VOICE_ID
    agent_config.voice_model       = DEFAULT_AGENT_VOICE_MODEL
    # agent_config.voice_temperature = 0.5
    # agent_config.voice_speed       = 1.0
    # agent_config.responsiveness    = 0.5
    # agent_config.backchannel_words = []
    # agent_config.ambient_sound     = None
    # agent_config.ambient_sound_volume = None
    agent_config.language          = DEFAULT_AGENT_LANG
    return agent_config
