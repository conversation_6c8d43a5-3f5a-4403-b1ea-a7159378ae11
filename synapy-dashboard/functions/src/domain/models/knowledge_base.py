from .base import SerializableModel
from typing import Optional, List

class KnowledgeBaseSource(SerializableModel):
    type: Optional[str] = None  # 'document', 'url', 'text'
    source_id: Optional[str] = None
    name: Optional[str] = None
    url: Optional[str] = None
    text: Optional[str] = None

class KnowledgeBase(SerializableModel):
    id: Optional[str] = None
    user_id: Optional[str] = None
    knowledge_base_id: Optional[str] = None  # External Retell ID
    name: Optional[str] = None
    type: Optional[str] = None  # 'inventory', 'process', 'faq', 'areas', 'policies', 'custom'
    custom_trigger: Optional[str] = None
    status: Optional[str] = None  # 'in_progress', 'ready', 'error'
    sources: Optional[List[KnowledgeBaseSource]] = None
    last_refreshed_timestamp: Optional[int] = None
