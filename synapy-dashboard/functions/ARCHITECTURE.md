# Firebase Functions Architecture

This document describes the refactored architecture of the Firebase Functions following hexagonal architecture principles.

## Overview

The codebase follows a layered architecture pattern with clear separation of concerns:

```
functions/
├── main.py                     # Entry point - imports and exposes all functions
├── src/
│   ├── domain/                 # Domain layer (business logic)
│   │   ├── models/            # Domain entities
│   │   ├── ports/             # Repository interfaces
│   │   └── services/          # Business logic services
│   ├── infrastructure/        # Infrastructure layer (external concerns)
│   │   ├── firebase/          # Firestore repositories
│   │   │   ├── assistant/     # Assistant-specific Firebase operations
│   │   │   ├── knowledge_base/# Knowledge base-specific Firebase operations
│   │   │   └── shared/        # Common Firebase utilities
│   │   ├── retell/           # Retell AI repositories
│   │   │   ├── assistant/     # Assistant-specific Retell operations
│   │   │   ├── knowledge_base/# Knowledge base-specific Retell operations
│   │   │   └── shared/        # Common Retell utilities
│   │   └── http/             # HTTP decorators and middleware
│   ├── app/                   # Application layer (use cases)
│   │   ├── functions/         # Firebase Function definitions
│   │   ├── handlers/          # Request handlers with DI
│   │   ├── models/           # Response models and requests
│   │   │   └── requests/      # Centralized request models by domain
│   │   │       ├── assistant/ # Assistant request models
│   │   │       └── knowledge_base/ # Knowledge base request models
│   │   └── config/           # Function registry and configuration
│   └── shared/               # Shared utilities
│       ├── container.py      # Dependency injection container
│       ├── config.py         # Application configuration
│       └── logging/          # Logging utilities
```

## Key Principles

### 1. Hexagonal Architecture
- **Domain layer**: Contains business logic, independent of external concerns
- **Infrastructure layer**: Handles external dependencies (databases, APIs, HTTP)
- **Application layer**: Orchestrates use cases and handles requests

### 2. Dependency Injection
- Services are injected through a container pattern
- Easy to mock for testing
- Clear dependency management

### 3. Separation of Concerns
- HTTP concerns separated from business logic
- Each function has a single responsibility
- Clear boundaries between layers

### 4. Domain Separation
- Infrastructure layer organized by domain (assistant, knowledge_base)
- Each domain has its own models, mappers, clients, and repositories
- Shared utilities extracted to common modules
- Request models centralized and organized by domain

## Components

### HTTP Infrastructure (`src/infrastructure/http/`)

Provides decorators and middleware for Firebase Functions:

```python
@authenticated_firebase_function
def create_assistant_http(request_data: dict) -> dict:
    return _assistant_handlers.create_assistant(request_data)
```

### Handlers (`src/app/handlers/`)

Handle request validation and orchestrate service calls:

```python
class AssistantHandlers(BaseHandler):
    def create_assistant(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        dto = self.validate_request(CreateAssistantRequest, request_data)
        service = self.get_service(AssistantService)
        assistant = service.create(dto.to_domain())
        return self.create_response(AssistantResponse, assistant)
```

### Service Container (`src/shared/container.py`)

Manages dependency injection:

```python
# Get a service
service = get_service(AssistantService)

# Register a custom factory
register_service_factory(CustomService, lambda: CustomService())
```

### Function Organization (`src/app/functions/`)

Functions are organized by domain:
- `assistant_functions.py` - Assistant-related endpoints
- `knowledge_base_functions.py` - Knowledge base-related endpoints

### Infrastructure Domain Organization

#### Firebase Infrastructure (`src/infrastructure/firebase/`)
- **`assistant/`**: Assistant-specific Firestore operations
  - `assistant_models.py` - Firestore data models
  - `assistant_mappers.py` - Domain ↔ Firestore mapping
  - `assistant_client.py` - Firestore client operations
  - `assistant_repository.py` - Repository implementation
- **`knowledge_base/`**: Knowledge base-specific Firestore operations
  - `knowledge_base_models.py` - Firestore data models
  - `knowledge_base_mappers.py` - Domain ↔ Firestore mapping
  - `knowledge_base_client.py` - Firestore client operations
  - `knowledge_base_repository.py` - Repository implementation
- **`shared/`**: Common Firebase utilities
  - `base_client.py` - Base Firestore client with common operations
  - `exceptions.py` - Firebase-specific exceptions

#### Retell Infrastructure (`src/infrastructure/retell/`)
- **`assistant/`**: Assistant-specific Retell AI operations
  - `assistant_models.py` - Retell API data models
  - `assistant_mappers.py` - Domain ↔ Retell mapping
  - `assistant_client.py` - Retell API client operations
  - `assistant_repository.py` - Repository implementation
- **`knowledge_base/`**: Knowledge base-specific Retell AI operations
  - `knowledge_base_models.py` - Retell API data models
  - `knowledge_base_mappers.py` - Domain ↔ Retell mapping
  - `knowledge_base_client.py` - Retell API client operations
  - `knowledge_base_repository.py` - Repository implementation
- **`shared/`**: Common Retell utilities
  - `retell_client.py` - Base Retell API client
  - `exceptions.py` - Retell-specific exceptions
  - `base_models.py` - Base model classes

### Request Models Organization (`src/app/models/requests/`)
- **`assistant/`**: All assistant-related request models
  - `create_assistant_request.py`
  - `update_assistant_request.py`
  - `get_assistant_request.py`
  - `delete_assistant_request.py`
  - `fetch_assistants_request.py`
- **`knowledge_base/`**: All knowledge base-related request models
  - `create_knowledge_base_request.py`
  - `update_knowledge_base_request.py`
  - `get_knowledge_base_request.py`
  - `delete_knowledge_base_request.py`
  - `fetch_knowledge_bases_request.py`

## Adding New Functions

### 1. Create Domain Models
```python
# src/domain/models/new_entity.py
class NewEntity(SerializableModel):
    id: Optional[str] = None
    name: Optional[str] = None
```

### 2. Create Repository Interface
```python
# src/domain/ports/new_entity_repository.py
class NewEntityRepository(ABC):
    @abstractmethod
    def save(self, entity: NewEntity) -> NewEntity:
        pass
```

### 3. Implement Infrastructure
```python
# src/infrastructure/firebase/new_entity_repository.py
class FirestoreNewEntityRepository(NewEntityRepository):
    def save(self, entity: NewEntity) -> NewEntity:
        # Implementation
```

### 4. Create Service
```python
# src/domain/services/new_entity_service.py
class NewEntityService:
    def __init__(self):
        self.repository = get_service(NewEntityRepository)
```

### 5. Create Handler
```python
# src/app/handlers/new_entity_handlers.py
class NewEntityHandlers(BaseHandler):
    def create(self, request_data: dict) -> dict:
        # Implementation
```

### 6. Create Function
```python
# src/app/functions/new_entity_functions.py
@authenticated_firebase_function
def create_new_entity_http(request_data: dict) -> dict:
    return _handlers.create(request_data)
```

### 7. Export in main.py
```python
# main.py
from src.app.functions import create_new_entity_http
```

## Benefits

1. **Scalability**: Easy to add new functions and services
2. **Testability**: Clear dependency injection makes testing easier
3. **Maintainability**: Separation of concerns reduces coupling
4. **Consistency**: Standardized patterns across all functions
5. **Reusability**: Shared components reduce code duplication

## Testing Strategy

- **Unit tests**: Test domain services in isolation
- **Integration tests**: Test handlers with mocked dependencies
- **End-to-end tests**: Test complete Firebase Functions

## Error Handling

Centralized error handling through decorators:
- Domain errors are converted to appropriate HTTP errors
- Consistent error responses across all functions
- Proper logging and monitoring
