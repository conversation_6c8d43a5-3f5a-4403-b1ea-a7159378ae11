rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    function signedInOrPublic() {
      return request.auth.uid != null || resource.data.visibility == 'public';
    }

    match /profile/{userId} {
      allow read, update, delete: if signedInOrPublic() && request.auth.uid == userId;
      allow create:               if signedInOrPublic();
    }
    match /assistants/{assistantId} {
      allow read:           if signedInOrPublic() && resource.data.userId == request.auth.uid;
      allow update, delete: if signedInOrPublic() && request.resource.data.userId == request.auth.uid;
      allow create:         if signedInOrPublic();
    }
  }
}
